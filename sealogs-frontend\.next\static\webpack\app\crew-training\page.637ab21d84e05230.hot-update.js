"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/list.tsx":
/*!*******************************************!*\
  !*** ./src/app/ui/crew-training/list.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OverdueTrainingList: function() { return /* binding */ OverdueTrainingList; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_filter__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/filter */ \"(app-pages-browser)/./src/components/filter/index.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./hooks/useTrainingFilters */ \"(app-pages-browser)/./src/app/ui/crew-training/hooks/useTrainingFilters.ts\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_ed8daac48216b87d589b3ebdbcc06997/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/app/lib/vessel-icon-helper */ \"(app-pages-browser)/./src/app/lib/vessel-icon-helper.tsx\");\n/* harmony import */ var _components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./components/mobile-training-card */ \"(app-pages-browser)/./src/app/ui/crew-training/components/mobile-training-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,OverdueTrainingList auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CrewTrainingList = (param)=>{\n    let { memberId = 0, vesselId = 0, applyFilterRef, excludeFilters = [] } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const limit = 100;\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [pageInfo, setPageInfo] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        totalCount: 0,\n        hasNextPage: false,\n        hasPreviousPage: false\n    });\n    const [trainingList, setTrainingList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingSessionDues, setTrainingSessionDues] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    // const [filter, setFilter] = useState({})\n    const [vesselIdOptions, setVesselIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainingTypeIdOptions, setTrainingTypeIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [trainerIdOptions, setTrainerIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [crewIdOptions, setCrewIdOptions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [isVesselView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [overdueSwitcher, setOverdueSwitcher] = (0,nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState)(\"overdue\");\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const { getVesselWithIcon, loading: vesselDataLoading } = (0,_app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData)();\n    // Create a boolean state wrapper for the useTrainingFilters hook\n    const [overdueBoolean, setOverdueBoolean] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Sync the boolean state with the query state\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setOverdueBoolean(overdueSwitcher === \"true\");\n    }, [\n        overdueSwitcher\n    ]);\n    // Create a wrapper function that converts boolean to string for the query state\n    const toggleOverdueWrapper = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        if (typeof value === \"function\") {\n            setOverdueBoolean((prev)=>{\n                const newValue = value(prev);\n                setOverdueSwitcher(newValue ? \"true\" : \"false\");\n                return newValue;\n            });\n        } else {\n            setOverdueBoolean(value);\n            setOverdueSwitcher(value ? \"true\" : \"false\");\n        }\n    }, [\n        setOverdueSwitcher\n    ]);\n    const [queryTrainingList, { loading: trainingListLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.TRAINING_SESSIONS, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessions.nodes;\n            // Transform vessel data to include complete vessel information with position\n            const transformedData = data.map((item)=>{\n                const completeVesselData = getVesselWithIcon(item.vessel.id, item.vessel);\n                return {\n                    ...item,\n                    vessel: completeVesselData\n                };\n            });\n            const vesselIDs = Array.from(new Set(data.map((item)=>item.vessel.id))).filter((id)=>+id !== 0);\n            const trainingTypeIDs = Array.from(new Set(data.flatMap((item)=>item.trainingTypes.nodes.map((t)=>t.id))));\n            const trainerIDs = Array.from(new Set(data.map((item)=>item.trainerID))).filter((id)=>+id !== 0);\n            const memberIDs = Array.from(new Set(data.flatMap((item)=>item.members.nodes.map((t)=>t.id))));\n            if (transformedData) {\n                setTrainingList(transformedData);\n                setVesselIdOptions(vesselIDs);\n                setTrainingTypeIdOptions(trainingTypeIDs);\n                setTrainerIdOptions(trainerIDs);\n                setCrewIdOptions(memberIDs);\n            }\n            setPageInfo(response.readTrainingSessions.pageInfo);\n        },\n        onError: (error)=>{\n            console.error(\"queryTrainingList error\", error);\n        }\n    });\n    const loadTrainingList = async function() {\n        let startPage = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, searchFilter = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {\n            ...filter\n        };\n        await queryTrainingList({\n            variables: {\n                filter: searchFilter,\n                offset: startPage * limit,\n                limit: limit\n            }\n        });\n    };\n    const loadTrainingSessionDues = async (filter)=>{\n        const dueFilter = {};\n        if (memberId > 0) {\n            dueFilter.memberID = {\n                eq: +memberId\n            };\n        }\n        if (vesselId > 0) {\n            dueFilter.vesselID = {\n                eq: +vesselId\n            };\n        }\n        if (filter.vesselID) {\n            dueFilter.vesselID = filter.vesselID;\n        }\n        if (filter.trainingTypes) {\n            dueFilter.trainingTypeID = {\n                eq: filter.trainingTypes.id.contains\n            };\n        }\n        if (filter.members) {\n            dueFilter.memberID = {\n                eq: filter.members.id.contains\n            };\n        }\n        if (filter.date) {\n            dueFilter.dueDate = filter.date;\n        } else {\n            dueFilter.dueDate = {\n                ne: null\n            };\n        }\n        await readTrainingSessionDues({\n            variables: {\n                filter: dueFilter\n            }\n        });\n    };\n    const handleNavigationClick = (newPage)=>{\n        if (newPage < 0 || newPage === page) return;\n        setPage(newPage);\n        loadTrainingSessionDues(filter);\n        loadTrainingList(newPage, filter);\n    };\n    const { filter, setFilter, handleFilterChange } = (0,_hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters)({\n        initialFilter: {},\n        loadList: loadTrainingList,\n        loadDues: loadTrainingSessionDues,\n        toggleOverdue: toggleOverdueWrapper\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (applyFilterRef) applyFilterRef.current = {\n            apply: handleFilterChange,\n            overdue: overdueBoolean,\n            setOverdue: toggleOverdueWrapper\n        };\n    }, [\n        handleFilterChange,\n        overdueBoolean,\n        toggleOverdueWrapper\n    ]);\n    const [readTrainingSessionDues, { loading: trainingSessionDuesLoading }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_1__.READ_TRAINING_SESSION_DUES, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const data = response.readTrainingSessionDues.nodes;\n            if (data) {\n                // Filter out crew members who are no longer assigned to the vessel.\n                const filteredData = data.filter((item)=>item.vessel.seaLogsMembers.nodes.some((m)=>{\n                        return m.id === item.memberID;\n                    }));\n                const dueWithStatus = filteredData.map((due)=>{\n                    return {\n                        ...due,\n                        status: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_4__.GetTrainingSessionStatus)(due)\n                    };\n                });\n                // Return only due within 7 days and overdue\n                // const filteredDueWithStatus = dueWithStatus.filter(\n                //     (item: any) => {\n                //         return (\n                //             item.status.isOverdue ||\n                //             (item.status.isOverdue === false &&\n                //                 item.status.dueWithinSevenDays === true)\n                //         )\n                //     },\n                // )\n                // const groupedDues = filteredDueWithStatus.reduce(\n                const groupedDues = dueWithStatus.reduce((acc, due)=>{\n                    const key = \"\".concat(due.vesselID, \"-\").concat(due.trainingTypeID, \"-\").concat(due.dueDate);\n                    if (!acc[key]) {\n                        acc[key] = {\n                            id: due.id,\n                            vesselID: due.vesselID,\n                            vessel: due.vessel,\n                            trainingTypeID: due.trainingTypeID,\n                            trainingType: due.trainingType,\n                            dueDate: due.dueDate,\n                            status: due.status,\n                            trainingLocationType: due.trainingSession.trainingLocationType,\n                            members: []\n                        };\n                    }\n                    acc[key].members.push(due.member);\n                    return acc;\n                }, {});\n                const mergedDues = Object.values(groupedDues).map((group)=>{\n                    const mergedMembers = group.members.reduce((acc, member)=>{\n                        const existingMember = acc.find((m)=>m.id === member.id);\n                        if (existingMember) {\n                            existingMember.firstName = member.firstName;\n                            existingMember.surname = member.surname;\n                        } else {\n                            acc.push(member);\n                        }\n                        return acc;\n                    }, []);\n                    return {\n                        id: group.id,\n                        vesselID: group.vesselID,\n                        vessel: group.vessel,\n                        trainingTypeID: group.trainingTypeID,\n                        trainingType: group.trainingType,\n                        status: group.status,\n                        dueDate: group.dueDate,\n                        trainingLocationType: group.trainingLocationType,\n                        members: mergedMembers\n                    };\n                });\n                setTrainingSessionDues(mergedDues);\n            }\n        },\n        onError: (error)=>{\n            console.error(\"readTrainingSessionDues error\", error);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (isLoading) {\n            const f = {\n                ...filter\n            };\n            if (+memberId > 0) {\n                f.members = {\n                    id: {\n                        contains: +memberId\n                    }\n                };\n            }\n            setFilter(f);\n            loadTrainingSessionDues(f);\n            loadTrainingList(0, f);\n            setIsLoading(false);\n        }\n    }, [\n        isLoading\n    ]);\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.getPermissions);\n    }, []);\n    // Transform completed training sessions to match unified structure\n    const transformCompletedTrainings = (trainingList)=>{\n        return trainingList.map((training)=>{\n            var _training_vessel, _training_vessel1, _training_trainingTypes_nodes_, _training_trainingTypes_nodes, _training_trainingTypes, _training_trainingTypes_nodes1, _training_trainingTypes1, _training_members;\n            // Ensure vessel has complete data including position\n            const completeVesselData = getVesselWithIcon((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.id, training.vessel);\n            return {\n                id: training.id,\n                dueDate: training.date,\n                vesselID: (_training_vessel1 = training.vessel) === null || _training_vessel1 === void 0 ? void 0 : _training_vessel1.id,\n                vessel: completeVesselData,\n                trainingTypeID: (_training_trainingTypes = training.trainingTypes) === null || _training_trainingTypes === void 0 ? void 0 : (_training_trainingTypes_nodes = _training_trainingTypes.nodes) === null || _training_trainingTypes_nodes === void 0 ? void 0 : (_training_trainingTypes_nodes_ = _training_trainingTypes_nodes[0]) === null || _training_trainingTypes_nodes_ === void 0 ? void 0 : _training_trainingTypes_nodes_.id,\n                trainingType: ((_training_trainingTypes1 = training.trainingTypes) === null || _training_trainingTypes1 === void 0 ? void 0 : (_training_trainingTypes_nodes1 = _training_trainingTypes1.nodes) === null || _training_trainingTypes_nodes1 === void 0 ? void 0 : _training_trainingTypes_nodes1[0]) || {\n                    title: \"\"\n                },\n                members: ((_training_members = training.members) === null || _training_members === void 0 ? void 0 : _training_members.nodes) || [],\n                trainer: training.trainer,\n                status: {\n                    label: \"Completed\",\n                    isOverdue: false,\n                    class: \"border rounded border-border text-input bg-outer-space-50 p-2 items-center justify-center\",\n                    dueWithinSevenDays: false\n                },\n                trainingLocationType: training.trainingLocationType,\n                // Add priority for sorting (higher number = higher priority)\n                priority: 0\n            };\n        });\n    };\n    // Create unified and sorted training data\n    const createUnifiedTrainingData = ()=>{\n        // Transform completed trainings\n        const transformedCompleted = transformCompletedTrainings(trainingList || []);\n        // Add priority to overdue/upcoming trainings\n        const prioritizedDues = (trainingSessionDues || []).map((due)=>{\n            var _due_status, _due_status1;\n            return {\n                ...due,\n                priority: ((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? 3 : ((_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.dueWithinSevenDays) ? 2 : 1\n            };\n        });\n        // Combine all training data\n        const allTrainings = [\n            ...prioritizedDues,\n            ...transformedCompleted\n        ];\n        // Sort by priority (descending), then by due date (ascending for overdue/upcoming, descending for completed)\n        return allTrainings.sort((a, b)=>{\n            // First sort by priority (overdue > upcoming > other > completed)\n            if (a.priority !== b.priority) {\n                return b.priority - a.priority;\n            }\n            // Within same priority, sort by date\n            const dateA = new Date(a.dueDate || 0).getTime();\n            const dateB = new Date(b.dueDate || 0).getTime();\n            // For overdue and upcoming (priority > 0), sort by due date ascending (earliest first)\n            if (a.priority > 0) {\n                return dateA - dateB;\n            }\n            // For completed trainings (priority = 0), sort by date descending (most recent first)\n            return dateB - dateA;\n        });\n    };\n    // Combined loading state for unified view\n    const isUnifiedDataLoading = excludeFilters.includes(\"overdueToggle\") ? trainingListLoading || trainingSessionDuesLoading : false;\n    // Create unified dataset when overdueToggle is excluded\n    const getUnifiedTrainingData = ()=>{\n        if (excludeFilters.includes(\"overdueToggle\")) {\n            const transformedCompletedTraining = transformTrainingListToOverdueFormat(trainingList || []);\n            return [\n                ...trainingSessionDues || [],\n                ...transformedCompletedTraining\n            ];\n        }\n        return trainingSessionDues || [];\n    };\n    if (!permissions || !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"EDIT_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"RECORD_TRAINING\", permissions) && !(0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_5__.hasPermission)(\"VIEW_MEMBER_TRAINING\", permissions)) {\n        return !permissions ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 447,\n            columnNumber: 13\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            errorMessage: \"Oops You do not have the permission to view this section.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 449,\n            columnNumber: 13\n        }, undefined);\n    }\n    const columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: training,\n                    memberId: memberId,\n                    type: \"completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowB_original;\n                const dateA = new Date((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.date) || 0).getTime();\n                const dateB = new Date((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.date) || 0).getTime();\n                return dateB - dateA;\n            }\n        },\n        {\n            accessorKey: \"trainingDrillsCompleted\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Training/drills completed\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"tablet-md\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.P, {\n                    children: training.trainingTypes.nodes ? training.trainingTypes.nodes.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.title,\n                                \",\\xa0\"\n                            ]\n                        }, item.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 35\n                        }, undefined)) : \"\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_trainingTypes_nodes_, _rowA_original_trainingTypes_nodes, _rowA_original_trainingTypes, _rowA_original, _rowB_original_trainingTypes_nodes_, _rowB_original_trainingTypes_nodes, _rowB_original_trainingTypes, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_trainingTypes = _rowA_original.trainingTypes) === null || _rowA_original_trainingTypes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes = _rowA_original_trainingTypes.nodes) === null || _rowA_original_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_trainingTypes_nodes_ = _rowA_original_trainingTypes_nodes[0]) === null || _rowA_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_trainingTypes_nodes_.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_trainingTypes = _rowB_original.trainingTypes) === null || _rowB_original_trainingTypes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes = _rowB_original_trainingTypes.nodes) === null || _rowB_original_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_trainingTypes_nodes_ = _rowB_original_trainingTypes_nodes[0]) === null || _rowB_original_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_trainingTypes_nodes_.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"where\",\n            cellAlignment: \"left\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Where\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _training_vessel;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-nowrap\",\n                            children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || training.trainingLocationType || \"\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_14__.LocationModal, {\n                            vessel: training.vessel,\n                            iconClassName: \"size-8\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 523,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 517,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"trainer\",\n            cellAlignment: \"center\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Trainer\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 541,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                var _training_trainer_surname, _training_trainer_surname1;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-nowrap\",\n                    children: !isVesselView ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname = training.trainer.surname) !== null && _training_trainer_surname !== void 0 ? _training_trainer_surname : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 560,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                    size: \"sm\",\n                                    variant: \"secondary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                        className: \"text-sm\",\n                                        children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(training.trainer.firstName, training.trainer.surname)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                children: [\n                                    training.trainer.firstName,\n                                    \" \",\n                                    (_training_trainer_surname1 = training.trainer.surname) !== null && _training_trainer_surname1 !== void 0 ? _training_trainer_surname1 : \"\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                lineNumber: 577,\n                                columnNumber: 33\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original, _rowA_original_trainer, _rowA_original1, _rowA_original2, _rowA_original_trainer1, _rowA_original3, _rowB_original, _rowB_original_trainer, _rowB_original1, _rowB_original2, _rowB_original_trainer1, _rowB_original3;\n                const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : _rowA_original.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainer = _rowA_original1.trainer) === null || _rowA_original_trainer === void 0 ? void 0 : _rowA_original_trainer.firstName), \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original2 = rowA.original) === null || _rowA_original2 === void 0 ? void 0 : _rowA_original2.trainer) && (rowA === null || rowA === void 0 ? void 0 : (_rowA_original3 = rowA.original) === null || _rowA_original3 === void 0 ? void 0 : (_rowA_original_trainer1 = _rowA_original3.trainer) === null || _rowA_original_trainer1 === void 0 ? void 0 : _rowA_original_trainer1.surname)) || \"\";\n                const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : _rowB_original.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainer = _rowB_original1.trainer) === null || _rowB_original_trainer === void 0 ? void 0 : _rowB_original_trainer.firstName), \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original2 = rowB.original) === null || _rowB_original2 === void 0 ? void 0 : _rowB_original2.trainer) && (rowB === null || rowB === void 0 ? void 0 : (_rowB_original3 = rowB.original) === null || _rowB_original3 === void 0 ? void 0 : (_rowB_original_trainer1 = _rowB_original3.trainer) === null || _rowB_original_trainer1 === void 0 ? void 0 : _rowB_original_trainer1.surname)) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"who\",\n            cellAlignment: \"right\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Who\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 600,\n                    columnNumber: 17\n                }, undefined);\n            },\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                const training = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full flex items-end gap-1\",\n                    children: training.members.nodes.map((member, index)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 45\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 611,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 623,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 37\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 606,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        }\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter__WEBPACK_IMPORTED_MODULE_3__.TrainingListFilter, {\n                        memberId: memberId,\n                        onChange: handleFilterChange,\n                        overdueSwitcher: !overdueBoolean,\n                        excludeFilters: excludeFilters\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 649,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 648,\n                columnNumber: 13\n            }, undefined),\n            excludeFilters.includes(\"overdueToggle\") ? isUnifiedDataLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 661,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading training data...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 660,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: getUnifiedTrainingData(),\n                hideCrewColumn: true,\n                pageSize: 20\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 665,\n                columnNumber: 21\n            }, undefined) : overdueBoolean ? trainingSessionDuesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center p-8 text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        className: \"mr-2 h-4 w-4 animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 674,\n                        columnNumber: 25\n                    }, undefined),\n                    \"Loading overdue training...\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 673,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(OverdueTrainingList, {\n                trainingSessionDues: trainingSessionDues\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 678,\n                columnNumber: 21\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: (trainingList === null || trainingList === void 0 ? void 0 : trainingList.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: trainingList,\n                    pageSize: 20,\n                    onChange: handleFilterChange,\n                    showToolbar: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 685,\n                    columnNumber: 25\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"group border-b hover: \",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"!w-[75px] h-auto\",\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    viewBox: \"0 0 147 147.01\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 700,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                            fill: \"#052350\",\n                                            fillRule: \"evenodd\",\n                                            opacity: \".97\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 712,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                            fill: \"#024450\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                            fill: \"#052451\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                            fill: \"#2998e9\",\n                                            strokeWidth: \"0px\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 737,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 742,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                            fill: \"#052250\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 747,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                            fill: \"#052350\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 752,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                            fill: \"#2a99ea\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 767,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                            fill: \"#ffffff\",\n                                            strokeWidth: \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 772,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"  \",\n                                    children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 33\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false),\n            !excludeFilters.includes(\"overdueToggle\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-background border border-border p-5 text-center hover:text-light-blue-vivid-900 w-full\",\n                onClick: ()=>toggleOverdueWrapper((prev)=>!prev),\n                children: overdueBoolean ? \"View all completed trainings\" : \"View overdue trainings\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 790,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n        lineNumber: 647,\n        columnNumber: 9\n    }, undefined);\n};\n_s(CrewTrainingList, \"MHQV5rmcFGZBtb2m8YD7JtdoAFo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        nuqs__WEBPACK_IMPORTED_MODULE_17__.useQueryState,\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery,\n        _app_lib_vessel_icon_helper__WEBPACK_IMPORTED_MODULE_15__.useVesselIconData,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _hooks_useTrainingFilters__WEBPACK_IMPORTED_MODULE_13__.useTrainingFilters,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery\n    ];\n});\n_c = CrewTrainingList;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CrewTrainingList);\nconst OverdueTrainingList = (param)=>{\n    let { trainingSessionDues, isVesselView = false, hideCrewColumn = false, pageSize = 20 } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery)(\"(min-width: 720px)\");\n    const allColumns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"title\",\n            header: \"Training\",\n            cell: (param)=>{\n                let { row } = param;\n                const data = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_mobile_training_card__WEBPACK_IMPORTED_MODULE_16__.MobileTrainingCard, {\n                    data: data,\n                    type: \"overdue\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 818,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"vessel\",\n            cellAlignment: \"left\",\n            header: \"Vessel\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_vessel;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: isVesselView == false && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:table-cell p-2 align-top lg:align-middle items-center text-left\",\n                        children: ((_due_vessel = due.vessel) === null || _due_vessel === void 0 ? void 0 : _due_vessel.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                        lineNumber: 831,\n                        columnNumber: 29\n                    }, undefined)\n                }, void 0, false);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"crew\",\n            cellAlignment: \"right\",\n            header: \"Crew\",\n            breakpoint: \"laptop\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status;\n                const due = row.original;\n                const members = due.members || [];\n                return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1\",\n                    children: members.map((member)=>{\n                        var _member_surname;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipTrigger, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.Avatar, {\n                                        size: \"sm\",\n                                        variant: due.status.isOverdue ? \"destructive\" : \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_11__.getCrewInitials)(member.firstName, member.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 45\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 858,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_11__.TooltipContent, {\n                                    children: [\n                                        member.firstName,\n                                        \" \",\n                                        (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 874,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, member.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 857,\n                            columnNumber: 33\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 854,\n                    columnNumber: 21\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_12__.cn)(\"!rounded-full size-10\", (_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.class),\n                    children: members.length\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 883,\n                    columnNumber: 21\n                }, undefined);\n            },\n            sortingFn: (rowA, rowB)=>{\n                var _rowA_original_members_nodes_, _rowA_original_members_nodes, _rowA_original_members, _rowA_original, _rowA_original_members_nodes_1, _rowA_original_members_nodes1, _rowA_original_members1, _rowA_original1, _rowB_original_members_nodes_, _rowB_original_members_nodes, _rowB_original_members, _rowB_original, _rowB_original_members_nodes_1, _rowB_original_members_nodes1, _rowB_original_members1, _rowB_original1;\n                var _rowA_original_members_nodes__firstName, _rowA_original_members_nodes__surname;\n                const valueA = \"\".concat((_rowA_original_members_nodes__firstName = rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_members = _rowA_original.members) === null || _rowA_original_members === void 0 ? void 0 : (_rowA_original_members_nodes = _rowA_original_members.nodes) === null || _rowA_original_members_nodes === void 0 ? void 0 : (_rowA_original_members_nodes_ = _rowA_original_members_nodes[0]) === null || _rowA_original_members_nodes_ === void 0 ? void 0 : _rowA_original_members_nodes_.firstName) !== null && _rowA_original_members_nodes__firstName !== void 0 ? _rowA_original_members_nodes__firstName : \"\", \" \").concat((_rowA_original_members_nodes__surname = rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_members1 = _rowA_original1.members) === null || _rowA_original_members1 === void 0 ? void 0 : (_rowA_original_members_nodes1 = _rowA_original_members1.nodes) === null || _rowA_original_members_nodes1 === void 0 ? void 0 : (_rowA_original_members_nodes_1 = _rowA_original_members_nodes1[0]) === null || _rowA_original_members_nodes_1 === void 0 ? void 0 : _rowA_original_members_nodes_1.surname) !== null && _rowA_original_members_nodes__surname !== void 0 ? _rowA_original_members_nodes__surname : \"\") || \"\";\n                var _rowB_original_members_nodes__firstName, _rowB_original_members_nodes__surname;\n                const valueB = \"\".concat((_rowB_original_members_nodes__firstName = rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_members = _rowB_original.members) === null || _rowB_original_members === void 0 ? void 0 : (_rowB_original_members_nodes = _rowB_original_members.nodes) === null || _rowB_original_members_nodes === void 0 ? void 0 : (_rowB_original_members_nodes_ = _rowB_original_members_nodes[0]) === null || _rowB_original_members_nodes_ === void 0 ? void 0 : _rowB_original_members_nodes_.firstName) !== null && _rowB_original_members_nodes__firstName !== void 0 ? _rowB_original_members_nodes__firstName : \"\", \" \").concat((_rowB_original_members_nodes__surname = rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_members1 = _rowB_original1.members) === null || _rowB_original_members1 === void 0 ? void 0 : (_rowB_original_members_nodes1 = _rowB_original_members1.nodes) === null || _rowB_original_members_nodes1 === void 0 ? void 0 : (_rowB_original_members_nodes_1 = _rowB_original_members_nodes1[0]) === null || _rowB_original_members_nodes_1 === void 0 ? void 0 : _rowB_original_members_nodes_1.surname) !== null && _rowB_original_members_nodes__surname !== void 0 ? _rowB_original_members_nodes__surname : \"\") || \"\";\n                return valueA.localeCompare(valueB);\n            }\n        },\n        {\n            accessorKey: \"status\",\n            cellAlignment: \"right\",\n            header: \"Status\",\n            breakpoint: \"landscape\",\n            cell: (param)=>{\n                let { row } = param;\n                var _due_status, _due_status1, _due_status2;\n                const due = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\".concat(((_due_status = due.status) === null || _due_status === void 0 ? void 0 : _due_status.isOverdue) ? (_due_status1 = due.status) === null || _due_status1 === void 0 ? void 0 : _due_status1.class : \"\", \" rounded-md w-fit !p-2 text-nowrap\"),\n                    children: ((_due_status2 = due.status) === null || _due_status2 === void 0 ? void 0 : _due_status2.label) || \"Unknown Status\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 910,\n                    columnNumber: 21\n                }, undefined);\n            }\n        }\n    ]);\n    // Filter out crew column when hideCrewColumn is true\n    const columns = hideCrewColumn ? allColumns.filter((col)=>col.accessorKey !== \"crew\") : allColumns;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: (trainingSessionDues === null || trainingSessionDues === void 0 ? void 0 : trainingSessionDues.length) > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n            columns: columns,\n            data: trainingSessionDues,\n            pageSize: pageSize,\n            showToolbar: false\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 927,\n            columnNumber: 17\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"group border-b hover: \",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 col-span-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center gap-2 p-2 pt-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"!w-[75px] h-auto\",\n                            xmlns: \"http://www.w3.org/2000/svg\",\n                            viewBox: \"0 0 147 147.01\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M72.45,0c17.26-.07,32.68,5.12,46.29,15.56,10.6,8.39,18.38,18.88,23.35,31.47,5.08,13.45,6.21,27.23,3.41,41.34-3.23,15.08-10.38,27.92-21.44,38.52-12.22,11.42-26.69,18.01-43.44,19.78-15.66,1.42-30.31-1.75-43.95-9.52-13.11-7.73-22.98-18.44-29.61-32.13C.9,91.82-1.22,77.98.67,63.51c2.36-16.12,9.17-29.98,20.44-41.58C33.25,9.78,47.91,2.63,65.08.49c2.46-.27,4.91-.43,7.37-.49ZM82.49,19.46c-2.01-1.1-4.14-1.85-6.39-2.26-1.42-.15-2.84-.35-4.25-.61-1.46-.26-2.79-.81-4.01-1.63l-.35-.35c-.29-.53-.6-1.04-.93-1.54-.09.7-.16,1.41-.21,2.12.03.4.08.8.16,1.19.13.44.27.88.44,1.31-.5-.61-.86-1.29-1.1-2.05-.08-.4-.17-.78-.28-1.17-1.72.92-2.73,2.36-3.03,4.29-.15,1.3-.07,2.59.26,3.85-.01,0-.03.01-.05.02-1.2-.58-2.25-1.38-3.15-2.38-.35-.41-.7-.83-1.03-1.26-3.65,4.71-4.58,9.92-2.8,15.63.22.67.48,1.32.77,1.96-.88.9-1.32,1.99-1.31,3.27.07,2.46.06,4.91-.05,7.37,0,.73.15,1.41.49,2.05.5.66,1.14.84,1.91.51.04,1.08.14,2.15.28,3.22.32,1.6.91,3.09,1.77,4.48,1.02,1.69,2.3,3.17,3.83,4.43.03,2.55-.21,5.07-.75,7.56-.25,1.08-.6,2.12-1.07,3.13-.06-.82-.08-1.65-.07-2.47-3.51,1.06-7.03,2.13-10.55,3.2-.05.18-.05.35,0,.54-3,1.03-5.75,2.5-8.26,4.41-2.49,1.95-4.29,4.41-5.39,7.4-1.44,3.7-2.48,7.51-3.13,11.43-.85,5.13-1.39,10.29-1.59,15.49-.28,6.88-.27,13.75.05,20.62-11.85-8.19-20.56-18.94-26.13-32.24C1.06,87.19-.22,73.03,2.77,58.47c3.41-15.3,10.86-28.21,22.37-38.71C37.53,8.77,52.05,2.64,68.68,1.38c16.31-.96,31.27,3.03,44.89,11.95,12.77,8.65,21.95,20.17,27.55,34.55,5.1,13.75,6.03,27.78,2.8,42.09-3.66,15.08-11.25,27.73-22.79,37.96-2.17,1.88-4.43,3.63-6.79,5.25.2-5.25.26-10.51.19-15.77-.08-6.3-.58-12.57-1.49-18.8-.61-4.17-1.64-8.23-3.08-12.18-.63-1.7-1.43-3.3-2.43-4.81-1.72-2.2-3.8-3.98-6.23-5.34-1.7-.97-3.47-1.78-5.32-2.43,0-.17,0-.34-.05-.51-3.51-1.07-7.03-2.14-10.55-3.2,0,.67,0,1.34-.02,2.01-.71-1.61-1.18-3.29-1.4-5.04-.28-1.92-.4-3.85-.37-5.79,3.51-3.05,5.38-6.9,5.6-11.57,1.09.43,1.85.11,2.29-.98.14-.36.23-.74.28-1.12.16-2.71.39-5.42.68-8.12.02-1.16-.35-2.16-1.12-3.01.72-2,.98-4.06.77-6.18-.23-3.02-.99-5.9-2.29-8.63-.25-.49-.6-.89-1.05-1.19-.9-.57-1.85-1.05-2.85-1.45-2.32-.93-4.66-1.69-7-2.29l2.94,2.1c.23.19.44.38.65.58ZM67.79,16.43c1.57.82,3.23,1.33,4.99,1.56,3.64.17,7,1.21,10.08,3.13.46.32.91.64,1.35.98.51.5,1.04.98,1.59,1.42-.16-.79-.37-1.58-.63-2.38-.2-.45-.44-.88-.72-1.28,1.17.37,2.29.87,3.36,1.49.51.3.88.73,1.1,1.28,1.49,3.35,2.14,6.85,1.96,10.5-.1,1.56-.58,3-1.45,4.29.18-3.13-.99-5.59-3.52-7.4-.08-.03-.15-.03-.23,0-4.07,1.24-8.23,2.1-12.46,2.57-2.13.23-4.26.21-6.39-.05-1.36-.17-2.6-.64-3.73-1.4-.21-.16-.4-.34-.58-.54-.19-.26-.38-.5-.58-.75-1.64.95-2.79,2.32-3.43,4.11-.3.85-.5,1.72-.61,2.61-1.41-2.86-1.97-5.88-1.68-9.05.29-2.38,1.11-4.56,2.45-6.53,1.01,1.13,2.2,2.04,3.55,2.73.78.31,1.59.5,2.43.58-.41-.98-.7-1.99-.86-3.03-.2-1.18-.11-2.33.28-3.45.21-.49.49-.92.84-1.31.7,1.83,1.95,3.13,3.76,3.9.83.28,1.67.51,2.52.7-.5-.54-1.01-1.07-1.52-1.61-.82-.9-1.43-1.93-1.84-3.08ZM59.06,37.38c.02-1.89.61-3.59,1.75-5.09.27-.27.54-.54.82-.79.95.91,2.07,1.54,3.36,1.89,1.62.42,3.27.61,4.95.58,2.57-.05,5.12-.3,7.65-.77,2.69-.48,5.34-1.11,7.96-1.89,1.99,1.57,2.86,3.62,2.64,6.16-1.77-1.75-3.9-2.51-6.39-2.26-.64.04-1.28.12-1.91.23-4.21.03-8.43.03-12.65,0-1.36-.26-2.73-.32-4.11-.19-1.57.32-2.92,1.02-4.06,2.12ZM70.63,36.68c1.94-.06,3.88-.06,5.83-.02-.65.41-1.14.96-1.47,1.66-.32-.55-.8-.86-1.42-.93-.27,0-.52.07-.75.21-.28.21-.51.45-.7.72-.34-.7-.84-1.24-1.49-1.63ZM90.65,37.75s.08,0,.12.05c.4.71.54,1.47.42,2.29-.28,2.48-.5,4.97-.65,7.47-.04.39-.17.75-.37,1.07-.05.06-.12.1-.19.14-.28-.12-.54-.28-.75-.51-.03-.92-.03-1.83,0-2.75.77-1.63.95-3.33.56-5.09-.1-.38-.23-.76-.4-1.12.48-.47.9-.98,1.26-1.54ZM57.06,37.8c.07.02.13.07.16.14.14.28.29.54.47.79.03.23.03.47,0,.7-.64,1.67-.7,3.37-.19,5.09,0,1.24.03,2.47.07,3.71-.01.07-.03.14-.05.21-.18.14-.38.25-.61.33-.16-.06-.26-.16-.3-.33-.14-.39-.21-.8-.21-1.21.1-2.4.12-4.81.05-7.21-.03-.81.18-1.54.61-2.22ZM73.48,38.59c.14,0,.26.07.35.19.37.52.63,1.1.79,1.73.35,2.87,1.61,5.26,3.76,7.16,2.84,2.21,5.77,2.32,8.77.33.28-.22.56-.47.82-.72.41,6.51-2.13,11.48-7.63,14.91-3.24,1.68-6.66,2.21-10.27,1.61-2.37-.47-4.43-1.5-6.21-3.1-1.87-1.68-3.29-3.69-4.27-6-.48-1.29-.73-2.63-.75-4.01-.08-1.29-.11-2.58-.09-3.87,1.68,1.94,3.8,2.78,6.37,2.54,1.8-.35,3.31-1.2,4.55-2.54,1.55-1.71,2.48-3.72,2.8-6.02.16-.82.49-1.55,1-2.19ZM64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26ZM82.3,62.29s.06.05.07.09c.02,2.8.39,5.56,1.12,8.26.37,1.28.92,2.46,1.66,3.55-.38,3.03-1.34,5.86-2.87,8.49-1.97,3.15-4.79,5.04-8.47,5.67-2.56-.19-4.8-1.12-6.72-2.8-1.84-1.76-3.19-3.85-4.04-6.28-.56-1.56-.95-3.17-1.17-4.81.49-.6.88-1.27,1.17-2.01.74-1.94,1.2-3.95,1.4-6.02.13-1.16.2-2.33.23-3.5.03-.04.07-.05.12-.02,1.95,1.3,4.09,2.05,6.44,2.24,3.31.29,6.45-.3,9.43-1.77.58-.32,1.12-.69,1.63-1.1ZM95.83,75.08c2.89,1.03,5.53,2.49,7.93,4.36,1.73,1.39,3.07,3.07,4.04,5.06,1.47,3.25,2.56,6.62,3.27,10.13.98,4.87,1.62,9.78,1.91,14.74.51,8.23.53,16.46.05,24.68-13.72,8.81-28.73,12.66-45.05,11.55-12.33-.99-23.66-4.84-33.99-11.55-.43-8.31-.4-16.62.09-24.92.3-4.98.95-9.91,1.96-14.79.66-3.2,1.64-6.29,2.94-9.29.87-2.03,2.14-3.76,3.8-5.2,2.48-2.08,5.27-3.66,8.35-4.74.6,6.75.21,13.43-1.14,20.06-.41,2.14-.95,4.24-1.63,6.3-.38,1.08-.89,2.1-1.54,3.03-.28.33-.6.6-.96.82-.16.08-.34.13-.51.16v16.8h56.27v-16.8c-.58-.15-1.05-.46-1.42-.93-.7-.99-1.25-2.06-1.63-3.22-.74-2.26-1.31-4.56-1.73-6.91-1-4.99-1.41-10.03-1.21-15.12.04-1.42.11-2.83.21-4.25Z\",\n                                    fill: \"#052350\",\n                                    fillRule: \"evenodd\",\n                                    opacity: \".97\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M63.78,35.74c1.14,0,2.28.1,3.41.28v.61c1.76-.37,3.17.15,4.22,1.59.16.27.28.56.35.86-.17.49-.33.98-.47,1.47.18.08.36.13.56.14-.38,2.99-1.8,5.34-4.25,7.07-2.68,1.56-5.23,1.37-7.65-.56-1.64-1.53-2.37-3.42-2.17-5.67.14-1.59.81-2.92,1.98-3.99,1.16-1,2.5-1.6,4.01-1.8Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M82.07,35.74c2.41-.13,4.41.71,6,2.52,1.27,1.71,1.65,3.61,1.12,5.69-.71,2.39-2.25,3.93-4.64,4.64-1.35.35-2.68.26-3.97-.28-1.83-.89-3.23-2.23-4.18-4.04-.65-1.19-1.03-2.47-1.14-3.83.19-.02.37-.06.56-.09-.11-.45-.25-.9-.42-1.33.23-.83.72-1.47,1.45-1.91.3-.18.61-.34.93-.47.71-.02,1.43-.03,2.15-.02v-.61c.72-.11,1.44-.2,2.15-.28Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M65.55,40.6c.97,0,1.45.48,1.42,1.45-.23.75-.73,1.07-1.52.96-.66-.27-.95-.76-.86-1.47.16-.48.48-.79.96-.93Z\",\n                                    fill: \"#024450\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M81.18,40.6c.7-.04,1.18.28,1.42.93.06,1.08-.45,1.57-1.52,1.47-.81-.37-1.05-.97-.72-1.8.21-.3.48-.5.82-.61Z\",\n                                    fill: \"#052451\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 968,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M62.84,50.25h21.23c.1,3.78-1.35,6.8-4.34,9.08-3,2.03-6.23,2.51-9.71,1.45-3.65-1.35-5.96-3.91-6.93-7.68-.18-.94-.27-1.89-.26-2.85ZM64.1,51.47c.29,3.14,1.75,5.56,4.39,7.26,3.35,1.9,6.7,1.89,10.03-.05,2.59-1.7,4.03-4.11,4.34-7.21h-18.76Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 973,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M73.2,89.54c.19.06.37.06.56,0,4.36-.67,7.63-2.91,9.82-6.72,1.49-2.78,2.43-5.73,2.8-8.87l.21-2.24c2.7.85,5.4,1.68,8.12,2.47-.29,3.81-.36,7.62-.21,11.43.33,4.44,1.02,8.83,2.05,13.16.46,1.91,1.12,3.75,2.01,5.51.3.54.67,1.03,1.1,1.47.22.21.48.39.75.54v14.79h-53.85v-14.79c.54-.3.98-.7,1.33-1.21.56-.85,1.03-1.75,1.4-2.71.97-2.75,1.68-5.57,2.15-8.45.95-5.12,1.31-10.28,1.07-15.49-.04-1.36-.13-2.73-.26-4.08.01-.06.03-.11.05-.16,2.69-.83,5.38-1.66,8.07-2.47.16,3.36.91,6.58,2.26,9.66,1.25,2.77,3.15,4.96,5.72,6.56,1.51.86,3.13,1.4,4.85,1.61Z\",\n                                    fill: \"#2998e9\",\n                                    strokeWidth: \"0px\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 978,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M45.34,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 983,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M70.17,125.8h6.58v6.63h-6.58v-6.63Z\",\n                                    fill: \"#052250\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M77.77,125.8h23.84v6.63h-23.84v-6.63Z\",\n                                    fill: \"#052350\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 993,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M67.98,127.01v4.2h-21.42v-4.2h21.42Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 998,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M75.58,127.01v4.2h-4.2v-4.2h4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1003,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M78.99,127.01h21.42v4.2h-21.42v-4.2Z\",\n                                    fill: \"#2a99ea\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M64.1,51.47h18.76c-.31,3.1-1.75,5.51-4.34,7.21-3.33,1.93-6.68,1.95-10.03.05-2.64-1.7-4.1-4.12-4.39-7.26Z\",\n                                    fill: \"#ffffff\",\n                                    strokeWidth: \"0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 937,\n                            columnNumber: 29\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"  \",\n                            children: \"WOW! Look at that. All your crew are ship-shaped and trained to the gills. Great job, captain!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                            lineNumber: 1019,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                    lineNumber: 936,\n                    columnNumber: 25\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n                lineNumber: 935,\n                columnNumber: 21\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\list.tsx\",\n            lineNumber: 934,\n            columnNumber: 17\n        }, undefined)\n    }, void 0, false);\n};\n_s1(OverdueTrainingList, \"mDuYwCIVI9QBRqOzz3Dco716Kgk=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_18__.useMediaQuery\n    ];\n});\n_c1 = OverdueTrainingList;\nvar _c, _c1;\n$RefreshReg$(_c, \"CrewTrainingList\");\n$RefreshReg$(_c1, \"OverdueTrainingList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvY3Jldy10cmFpbmluZy9saXN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2dDO0FBQ2E7QUFDVztBQUVBO0FBQ0k7QUFDM0I7QUFDdUM7QUFFeEUsaURBQWlEO0FBQ2pELE1BQU1XLGFBQWEsQ0FBQ0M7SUFDaEIsSUFBSSxDQUFDQSxZQUFZLE9BQU87SUFDeEIsSUFBSTtRQUNBLE1BQU1DLE9BQU8sSUFBSUMsS0FBS0Y7UUFDdEIsT0FBT0osOEVBQU1BLENBQUNLLE1BQU07SUFDeEIsRUFBRSxVQUFNO1FBQ0osT0FBTztJQUNYO0FBQ0o7QUFDbUM7QUFDUTtBQUtSO0FBQ3NDO0FBQ25DO0FBVWQ7QUFDdUI7QUFDWDtBQUMyQjtBQUMzQjtBQUNXO0FBQ2lCO0FBRU07QUFFdEUsTUFBTXVCLG1CQUFtQjtRQUFDLEVBQ3RCQyxXQUFXLENBQUMsRUFDWkMsV0FBVyxDQUFDLEVBQ1pDLGNBQWMsRUFDZEMsaUJBQWlCLEVBQUUsRUFNdEI7O0lBQ0csTUFBTUMsU0FBU3pCLDBEQUFTQTtJQUN4QixNQUFNMEIsUUFBUTtJQUNkLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHdkMsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDd0MsVUFBVUMsWUFBWSxHQUFHekMsK0NBQVFBLENBQUM7UUFDckMwQyxZQUFZO1FBQ1pDLGFBQWE7UUFDYkMsaUJBQWlCO0lBQ3JCO0lBQ0EsTUFBTSxDQUFDQyxjQUFjQyxnQkFBZ0IsR0FBRzlDLCtDQUFRQSxDQUFDLEVBQUU7SUFDbkQsTUFBTSxDQUFDK0MscUJBQXFCQyx1QkFBdUIsR0FBR2hELCtDQUFRQSxDQUFDLEVBQUU7SUFDakUsTUFBTSxDQUFDaUQsTUFBTUMsUUFBUSxHQUFHbEQsK0NBQVFBLENBQUM7SUFDakMsMkNBQTJDO0lBQzNDLE1BQU0sQ0FBQ21ELGlCQUFpQkMsbUJBQW1CLEdBQUdwRCwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3pELE1BQU0sQ0FBQ3FELHVCQUF1QkMseUJBQXlCLEdBQUd0RCwrQ0FBUUEsQ0FDOUQsRUFBRTtJQUVOLE1BQU0sQ0FBQ3VELGtCQUFrQkMsb0JBQW9CLEdBQUd4RCwrQ0FBUUEsQ0FBQyxFQUFFO0lBQzNELE1BQU0sQ0FBQ3lELGVBQWVDLGlCQUFpQixHQUFHMUQsK0NBQVFBLENBQUMsRUFBRTtJQUNyRCxNQUFNLENBQUMyRCxhQUFhLEdBQUczRCwrQ0FBUUEsQ0FBQztJQUNoQyxNQUFNLENBQUM0RCxpQkFBaUJDLG1CQUFtQixHQUFHbEMsb0RBQWFBLENBQUM7SUFDNUQsTUFBTW1DLFNBQVN0QywrREFBYUEsQ0FBQztJQUM3QixNQUFNLEVBQUV1QyxpQkFBaUIsRUFBRUMsU0FBU0MsaUJBQWlCLEVBQUUsR0FDbkRwQywrRUFBaUJBO0lBRXJCLGlFQUFpRTtJQUNqRSxNQUFNLENBQUNxQyxnQkFBZ0JDLGtCQUFrQixHQUFHbkUsK0NBQVFBLENBQVU7SUFFOUQsOENBQThDO0lBQzlDRCxnREFBU0EsQ0FBQztRQUNOb0Usa0JBQWtCUCxvQkFBb0I7SUFDMUMsR0FBRztRQUFDQTtLQUFnQjtJQUVwQixnRkFBZ0Y7SUFDaEYsTUFBTVEsdUJBQXVCdEUsa0RBQVdBLENBQ3BDLENBQUN1RTtRQUNHLElBQUksT0FBT0EsVUFBVSxZQUFZO1lBQzdCRixrQkFBa0IsQ0FBQ0c7Z0JBQ2YsTUFBTUMsV0FBV0YsTUFBTUM7Z0JBQ3ZCVCxtQkFBbUJVLFdBQVcsU0FBUztnQkFDdkMsT0FBT0E7WUFDWDtRQUNKLE9BQU87WUFDSEosa0JBQWtCRTtZQUNsQlIsbUJBQW1CUSxRQUFRLFNBQVM7UUFDeEM7SUFDSixHQUNBO1FBQUNSO0tBQW1CO0lBR3hCLE1BQU0sQ0FBQ1csbUJBQW1CLEVBQUVSLFNBQVNTLG1CQUFtQixFQUFFLENBQUMsR0FBRzVFLDZEQUFZQSxDQUN0RUYscUVBQWlCQSxFQUNqQjtRQUNJK0UsYUFBYTtRQUNiQyxhQUFhLENBQUNDO1lBQ1YsTUFBTUMsT0FBT0QsU0FBU0Usb0JBQW9CLENBQUNDLEtBQUs7WUFFaEQsNkVBQTZFO1lBQzdFLE1BQU1DLGtCQUFrQkgsS0FBS0ksR0FBRyxDQUFDLENBQUNDO2dCQUM5QixNQUFNQyxxQkFBcUJwQixrQkFDdkJtQixLQUFLRSxNQUFNLENBQUNDLEVBQUUsRUFDZEgsS0FBS0UsTUFBTTtnQkFHZixPQUFPO29CQUNILEdBQUdGLElBQUk7b0JBQ1BFLFFBQVFEO2dCQUNaO1lBQ0o7WUFFQSxNQUFNRyxZQUFZQyxNQUFNQyxJQUFJLENBQ3hCLElBQUlDLElBQUlaLEtBQUtJLEdBQUcsQ0FBQyxDQUFDQyxPQUFjQSxLQUFLRSxNQUFNLENBQUNDLEVBQUUsSUFDaERLLE1BQU0sQ0FBQyxDQUFDTCxLQUFZLENBQUNBLE9BQU87WUFDOUIsTUFBTU0sa0JBQWtCSixNQUFNQyxJQUFJLENBQzlCLElBQUlDLElBQ0FaLEtBQUtlLE9BQU8sQ0FBQyxDQUFDVixPQUNWQSxLQUFLVyxhQUFhLENBQUNkLEtBQUssQ0FBQ0UsR0FBRyxDQUFDLENBQUNhLElBQVdBLEVBQUVULEVBQUU7WUFJekQsTUFBTVUsYUFBYVIsTUFBTUMsSUFBSSxDQUN6QixJQUFJQyxJQUFJWixLQUFLSSxHQUFHLENBQUMsQ0FBQ0MsT0FBY0EsS0FBS2MsU0FBUyxJQUNoRE4sTUFBTSxDQUFDLENBQUNMLEtBQVksQ0FBQ0EsT0FBTztZQUM5QixNQUFNWSxZQUFZVixNQUFNQyxJQUFJLENBQ3hCLElBQUlDLElBQ0FaLEtBQUtlLE9BQU8sQ0FBQyxDQUFDVixPQUNWQSxLQUFLZ0IsT0FBTyxDQUFDbkIsS0FBSyxDQUFDRSxHQUFHLENBQUMsQ0FBQ2EsSUFBV0EsRUFBRVQsRUFBRTtZQUtuRCxJQUFJTCxpQkFBaUI7Z0JBQ2pCbEMsZ0JBQWdCa0M7Z0JBQ2hCNUIsbUJBQW1Ca0M7Z0JBQ25CaEMseUJBQXlCcUM7Z0JBQ3pCbkMsb0JBQW9CdUM7Z0JBQ3BCckMsaUJBQWlCdUM7WUFDckI7WUFDQXhELFlBQVltQyxTQUFTRSxvQkFBb0IsQ0FBQ3RDLFFBQVE7UUFDdEQ7UUFDQTJELFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDN0M7SUFDSjtJQUdKLE1BQU1FLG1CQUFtQjtZQUNyQkMsNkVBQW9CLEdBQ3BCQyxnRkFBb0I7WUFBRSxHQUFHZCxNQUFNO1FBQUM7UUFFaEMsTUFBTWxCLGtCQUFrQjtZQUNwQmlDLFdBQVc7Z0JBQ1BmLFFBQVFjO2dCQUNSRSxRQUFRSCxZQUFZbEU7Z0JBQ3BCQSxPQUFPQTtZQUNYO1FBQ0o7SUFDSjtJQUVBLE1BQU1zRSwwQkFBMEIsT0FBT2pCO1FBQ25DLE1BQU1rQixZQUFpQixDQUFDO1FBQ3hCLElBQUk1RSxXQUFXLEdBQUc7WUFDZDRFLFVBQVVDLFFBQVEsR0FBRztnQkFBRUMsSUFBSSxDQUFDOUU7WUFBUztRQUN6QztRQUNBLElBQUlDLFdBQVcsR0FBRztZQUNkMkUsVUFBVUcsUUFBUSxHQUFHO2dCQUFFRCxJQUFJLENBQUM3RTtZQUFTO1FBQ3pDO1FBQ0EsSUFBSXlELE9BQU9xQixRQUFRLEVBQUU7WUFDakJILFVBQVVHLFFBQVEsR0FBR3JCLE9BQU9xQixRQUFRO1FBQ3hDO1FBQ0EsSUFBSXJCLE9BQU9HLGFBQWEsRUFBRTtZQUN0QmUsVUFBVUksY0FBYyxHQUFHO2dCQUFFRixJQUFJcEIsT0FBT0csYUFBYSxDQUFDUixFQUFFLENBQUM0QixRQUFRO1lBQUM7UUFDdEU7UUFDQSxJQUFJdkIsT0FBT1EsT0FBTyxFQUFFO1lBQ2hCVSxVQUFVQyxRQUFRLEdBQUc7Z0JBQUVDLElBQUlwQixPQUFPUSxPQUFPLENBQUNiLEVBQUUsQ0FBQzRCLFFBQVE7WUFBQztRQUMxRDtRQUNBLElBQUl2QixPQUFPbEYsSUFBSSxFQUFFO1lBQ2JvRyxVQUFVTSxPQUFPLEdBQUd4QixPQUFPbEYsSUFBSTtRQUNuQyxPQUFPO1lBQ0hvRyxVQUFVTSxPQUFPLEdBQUc7Z0JBQUVDLElBQUk7WUFBSztRQUNuQztRQUNBLE1BQU1DLHdCQUF3QjtZQUMxQlgsV0FBVztnQkFDUGYsUUFBUWtCO1lBQ1o7UUFDSjtJQUNKO0lBRUEsTUFBTVMsd0JBQXdCLENBQUNDO1FBQzNCLElBQUlBLFVBQVUsS0FBS0EsWUFBWXJFLE1BQU07UUFDckNDLFFBQVFvRTtRQUNSWCx3QkFBd0JqQjtRQUN4QlksaUJBQWlCZ0IsU0FBUzVCO0lBQzlCO0lBRUEsTUFBTSxFQUNGQSxNQUFNLEVBQ042QixTQUFTLEVBQ1RDLGtCQUFrQixFQUNyQixHQUFHOUYsOEVBQWtCQSxDQUFDO1FBQ25CK0YsZUFBZSxDQUFDO1FBQ2hCQyxVQUFVcEI7UUFDVnFCLFVBQVVoQjtRQUNWaUIsZUFBZXhEO0lBQ25CO0lBRUFyRSxnREFBU0EsQ0FBQztRQUNOLElBQUltQyxnQkFDQUEsZUFBZTJGLE9BQU8sR0FBRztZQUNyQkMsT0FBT047WUFDUE8sU0FBUzdEO1lBQ1Q4RCxZQUFZNUQ7UUFDaEI7SUFDUixHQUFHO1FBQUNvRDtRQUFvQnREO1FBQWdCRTtLQUFxQjtJQUU3RCxNQUFNLENBQUNnRCx5QkFBeUIsRUFBRXBELFNBQVNpRSwwQkFBMEIsRUFBRSxDQUFDLEdBQ3BFcEksNkRBQVlBLENBQUNELDhFQUEwQkEsRUFBRTtRQUNyQzhFLGFBQWE7UUFDYkMsYUFBYSxDQUFDQztZQUNWLE1BQU1DLE9BQU9ELFNBQVN3Qyx1QkFBdUIsQ0FBQ3JDLEtBQUs7WUFDbkQsSUFBSUYsTUFBTTtnQkFDTixvRUFBb0U7Z0JBQ3BFLE1BQU1xRCxlQUFlckQsS0FBS2EsTUFBTSxDQUFDLENBQUNSLE9BQzlCQSxLQUFLRSxNQUFNLENBQUMrQyxjQUFjLENBQUNwRCxLQUFLLENBQUNxRCxJQUFJLENBQUMsQ0FBQ0M7d0JBQ25DLE9BQU9BLEVBQUVoRCxFQUFFLEtBQUtILEtBQUsyQixRQUFRO29CQUNqQztnQkFFSixNQUFNeUIsZ0JBQWdCSixhQUFhakQsR0FBRyxDQUFDLENBQUNzRDtvQkFDcEMsT0FBTzt3QkFBRSxHQUFHQSxHQUFHO3dCQUFFQyxRQUFRdEksMEVBQXdCQSxDQUFDcUk7b0JBQUs7Z0JBQzNEO2dCQUNBLDRDQUE0QztnQkFDNUMsc0RBQXNEO2dCQUN0RCx1QkFBdUI7Z0JBQ3ZCLG1CQUFtQjtnQkFDbkIsdUNBQXVDO2dCQUN2QyxrREFBa0Q7Z0JBQ2xELDJEQUEyRDtnQkFDM0QsWUFBWTtnQkFDWixTQUFTO2dCQUNULElBQUk7Z0JBQ0osb0RBQW9EO2dCQUNwRCxNQUFNRSxjQUFjSCxjQUFjSSxNQUFNLENBQ3BDLENBQUNDLEtBQVVKO29CQUNQLE1BQU1LLE1BQU0sR0FBbUJMLE9BQWhCQSxJQUFJeEIsUUFBUSxFQUFDLEtBQXlCd0IsT0FBdEJBLElBQUl2QixjQUFjLEVBQUMsS0FBZSxPQUFadUIsSUFBSXJCLE9BQU87b0JBQ2hFLElBQUksQ0FBQ3lCLEdBQUcsQ0FBQ0MsSUFBSSxFQUFFO3dCQUNYRCxHQUFHLENBQUNDLElBQUksR0FBRzs0QkFDUHZELElBQUlrRCxJQUFJbEQsRUFBRTs0QkFDVjBCLFVBQVV3QixJQUFJeEIsUUFBUTs0QkFDdEIzQixRQUFRbUQsSUFBSW5ELE1BQU07NEJBQ2xCNEIsZ0JBQWdCdUIsSUFBSXZCLGNBQWM7NEJBQ2xDNkIsY0FBY04sSUFBSU0sWUFBWTs0QkFDOUIzQixTQUFTcUIsSUFBSXJCLE9BQU87NEJBQ3BCc0IsUUFBUUQsSUFBSUMsTUFBTTs0QkFDbEJNLHNCQUNJUCxJQUFJUSxlQUFlLENBQ2RELG9CQUFvQjs0QkFDN0I1QyxTQUFTLEVBQUU7d0JBQ2Y7b0JBQ0o7b0JBQ0F5QyxHQUFHLENBQUNDLElBQUksQ0FBQzFDLE9BQU8sQ0FBQzhDLElBQUksQ0FBQ1QsSUFBSVUsTUFBTTtvQkFDaEMsT0FBT047Z0JBQ1gsR0FDQSxDQUFDO2dCQUdMLE1BQU1PLGFBQWFDLE9BQU9DLE1BQU0sQ0FBQ1gsYUFBYXhELEdBQUcsQ0FDN0MsQ0FBQ29FO29CQUNHLE1BQU1DLGdCQUFnQkQsTUFBTW5ELE9BQU8sQ0FBQ3dDLE1BQU0sQ0FDdEMsQ0FBQ0MsS0FBVU07d0JBQ1AsTUFBTU0saUJBQWlCWixJQUFJYSxJQUFJLENBQzNCLENBQUNuQixJQUFXQSxFQUFFaEQsRUFBRSxLQUFLNEQsT0FBTzVELEVBQUU7d0JBRWxDLElBQUlrRSxnQkFBZ0I7NEJBQ2hCQSxlQUFlRSxTQUFTLEdBQ3BCUixPQUFPUSxTQUFTOzRCQUNwQkYsZUFBZUcsT0FBTyxHQUFHVCxPQUFPUyxPQUFPO3dCQUMzQyxPQUFPOzRCQUNIZixJQUFJSyxJQUFJLENBQUNDO3dCQUNiO3dCQUNBLE9BQU9OO29CQUNYLEdBQ0EsRUFBRTtvQkFFTixPQUFPO3dCQUNIdEQsSUFBSWdFLE1BQU1oRSxFQUFFO3dCQUNaMEIsVUFBVXNDLE1BQU10QyxRQUFRO3dCQUN4QjNCLFFBQVFpRSxNQUFNakUsTUFBTTt3QkFDcEI0QixnQkFBZ0JxQyxNQUFNckMsY0FBYzt3QkFDcEM2QixjQUFjUSxNQUFNUixZQUFZO3dCQUNoQ0wsUUFBUWEsTUFBTWIsTUFBTTt3QkFDcEJ0QixTQUFTbUMsTUFBTW5DLE9BQU87d0JBQ3RCNEIsc0JBQ0lPLE1BQU1QLG9CQUFvQjt3QkFDOUI1QyxTQUFTb0Q7b0JBQ2I7Z0JBQ0o7Z0JBRUp0Ryx1QkFBdUJrRztZQUMzQjtRQUNKO1FBQ0EvQyxTQUFTLENBQUNDO1lBQ05DLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ25EO0lBQ0o7SUFFSnJHLGdEQUFTQSxDQUFDO1FBQ04sSUFBSXVDLFdBQVc7WUFDWCxNQUFNcUgsSUFBdUI7Z0JBQUUsR0FBR2pFLE1BQU07WUFBQztZQUN6QyxJQUFJLENBQUMxRCxXQUFXLEdBQUc7Z0JBQ2YySCxFQUFFekQsT0FBTyxHQUFHO29CQUFFYixJQUFJO3dCQUFFNEIsVUFBVSxDQUFDakY7b0JBQVM7Z0JBQUU7WUFDOUM7WUFDQXVGLFVBQVVvQztZQUNWaEQsd0JBQXdCZ0Q7WUFDeEJyRCxpQkFBaUIsR0FBR3FEO1lBQ3BCcEgsYUFBYTtRQUNqQjtJQUNKLEdBQUc7UUFBQ0Q7S0FBVTtJQUVkLE1BQU0sQ0FBQ3NILGFBQWFDLGVBQWUsR0FBRzdKLCtDQUFRQSxDQUFNO0lBRXBERCxnREFBU0EsQ0FBQztRQUNOOEosZUFBZXpKLG1FQUFjQTtJQUNqQyxHQUFHLEVBQUU7SUFFTCxtRUFBbUU7SUFDbkUsTUFBTTBKLDhCQUE4QixDQUFDakg7UUFDakMsT0FBT0EsYUFBYW9DLEdBQUcsQ0FBQyxDQUFDOEU7Z0JBR2pCQSxrQkFPVUEsbUJBRU1BLGdDQUFBQSwrQkFBQUEseUJBQ0ZBLGdDQUFBQSwwQkFHTEE7WUFmYixxREFBcUQ7WUFDckQsTUFBTTVFLHFCQUFxQnBCLG1CQUN2QmdHLG1CQUFBQSxTQUFTM0UsTUFBTSxjQUFmMkUsdUNBQUFBLGlCQUFpQjFFLEVBQUUsRUFDbkIwRSxTQUFTM0UsTUFBTTtZQUduQixPQUFPO2dCQUNIQyxJQUFJMEUsU0FBUzFFLEVBQUU7Z0JBQ2Y2QixTQUFTNkMsU0FBU3ZKLElBQUk7Z0JBQ3RCdUcsUUFBUSxHQUFFZ0Qsb0JBQUFBLFNBQVMzRSxNQUFNLGNBQWYyRSx3Q0FBQUEsa0JBQWlCMUUsRUFBRTtnQkFDN0JELFFBQVFEO2dCQUNSNkIsY0FBYyxHQUFFK0MsMEJBQUFBLFNBQVNsRSxhQUFhLGNBQXRCa0UsK0NBQUFBLGdDQUFBQSx3QkFBd0JoRixLQUFLLGNBQTdCZ0YscURBQUFBLGlDQUFBQSw2QkFBK0IsQ0FBQyxFQUFFLGNBQWxDQSxxREFBQUEsK0JBQW9DMUUsRUFBRTtnQkFDdER3RCxjQUFja0IsRUFBQUEsMkJBQUFBLFNBQVNsRSxhQUFhLGNBQXRCa0UsZ0RBQUFBLGlDQUFBQSx5QkFBd0JoRixLQUFLLGNBQTdCZ0YscURBQUFBLDhCQUErQixDQUFDLEVBQUUsS0FBSTtvQkFDaERDLE9BQU87Z0JBQ1g7Z0JBQ0E5RCxTQUFTNkQsRUFBQUEsb0JBQUFBLFNBQVM3RCxPQUFPLGNBQWhCNkQsd0NBQUFBLGtCQUFrQmhGLEtBQUssS0FBSSxFQUFFO2dCQUN0Q2tGLFNBQVNGLFNBQVNFLE9BQU87Z0JBQ3pCekIsUUFBUTtvQkFDSjBCLE9BQU87b0JBQ1BDLFdBQVc7b0JBQ1hDLE9BQU87b0JBQ1BDLG9CQUFvQjtnQkFDeEI7Z0JBQ0F2QixzQkFBc0JpQixTQUFTakIsb0JBQW9CO2dCQUNuRCw2REFBNkQ7Z0JBQzdEd0IsVUFBVTtZQUNkO1FBQ0o7SUFDSjtJQUVBLDBDQUEwQztJQUMxQyxNQUFNQyw0QkFBNEI7UUFDOUIsZ0NBQWdDO1FBQ2hDLE1BQU1DLHVCQUF1QlYsNEJBQ3pCakgsZ0JBQWdCLEVBQUU7UUFHdEIsNkNBQTZDO1FBQzdDLE1BQU00SCxrQkFBa0IsQ0FBQzFILHVCQUF1QixFQUFFLEVBQUVrQyxHQUFHLENBQUMsQ0FBQ3NEO2dCQUUzQ0EsYUFFSkE7bUJBSjZEO2dCQUNuRSxHQUFHQSxHQUFHO2dCQUNOK0IsVUFBVS9CLEVBQUFBLGNBQUFBLElBQUlDLE1BQU0sY0FBVkQsa0NBQUFBLFlBQVk0QixTQUFTLElBQ3pCLElBQ0E1QixFQUFBQSxlQUFBQSxJQUFJQyxNQUFNLGNBQVZELG1DQUFBQSxhQUFZOEIsa0JBQWtCLElBQzVCLElBQ0E7WUFDWjs7UUFFQSw0QkFBNEI7UUFDNUIsTUFBTUssZUFBZTtlQUFJRDtlQUFvQkQ7U0FBcUI7UUFFbEUsNkdBQTZHO1FBQzdHLE9BQU9FLGFBQWFDLElBQUksQ0FBQyxDQUFDQyxHQUFRQztZQUM5QixrRUFBa0U7WUFDbEUsSUFBSUQsRUFBRU4sUUFBUSxLQUFLTyxFQUFFUCxRQUFRLEVBQUU7Z0JBQzNCLE9BQU9PLEVBQUVQLFFBQVEsR0FBR00sRUFBRU4sUUFBUTtZQUNsQztZQUVBLHFDQUFxQztZQUNyQyxNQUFNUSxRQUFRLElBQUlySyxLQUFLbUssRUFBRTFELE9BQU8sSUFBSSxHQUFHNkQsT0FBTztZQUM5QyxNQUFNQyxRQUFRLElBQUl2SyxLQUFLb0ssRUFBRTNELE9BQU8sSUFBSSxHQUFHNkQsT0FBTztZQUU5Qyx1RkFBdUY7WUFDdkYsSUFBSUgsRUFBRU4sUUFBUSxHQUFHLEdBQUc7Z0JBQ2hCLE9BQU9RLFFBQVFFO1lBQ25CO1lBRUEsc0ZBQXNGO1lBQ3RGLE9BQU9BLFFBQVFGO1FBQ25CO0lBQ0o7SUFFQSwwQ0FBMEM7SUFDMUMsTUFBTUcsdUJBQXVCOUksZUFBZStJLFFBQVEsQ0FBQyxtQkFDL0N6Ryx1QkFBdUJ3RCw2QkFDdkI7SUFFTix3REFBd0Q7SUFDeEQsTUFBTWtELHlCQUF5QjtRQUMzQixJQUFJaEosZUFBZStJLFFBQVEsQ0FBQyxrQkFBa0I7WUFDMUMsTUFBTUUsK0JBQ0ZDLHFDQUFxQ3hJLGdCQUFnQixFQUFFO1lBQzNELE9BQU87bUJBQ0NFLHVCQUF1QixFQUFFO21CQUMxQnFJO2FBQ047UUFDTDtRQUNBLE9BQU9ySSx1QkFBdUIsRUFBRTtJQUNwQztJQUVBLElBQ0ksQ0FBQzZHLGVBQ0EsQ0FBQ3ZKLHNFQUFhQSxDQUFDLGlCQUFpQnVKLGdCQUM3QixDQUFDdkosc0VBQWFBLENBQUMsaUJBQWlCdUosZ0JBQ2hDLENBQUN2SixzRUFBYUEsQ0FBQyxtQkFBbUJ1SixnQkFDbEMsQ0FBQ3ZKLHNFQUFhQSxDQUFDLHdCQUF3QnVKLGNBQzdDO1FBQ0UsT0FBTyxDQUFDQSw0QkFDSiw4REFBQ2xKLG9EQUFPQTs7OztzQ0FFUiw4REFBQ0Esb0RBQU9BO1lBQUM0SyxjQUFhOzs7Ozs7SUFFOUI7SUFFQSxNQUFNQyxVQUFVM0ssd0VBQWFBLENBQUM7UUFDMUI7WUFDSTRLLGFBQWE7WUFDYkMsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQzVLLG9GQUFtQkE7b0JBQUM0SyxRQUFRQTtvQkFBUTFCLE9BQU07Ozs7Ozs7WUFFL0MyQixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU03QixXQUFnQjZCLElBQUlDLFFBQVE7Z0JBQ2xDLHFCQUNJLDhEQUFDL0osaUZBQWtCQTtvQkFDZitDLE1BQU1rRjtvQkFDTi9ILFVBQVVBO29CQUNWOEosTUFBSzs7Ozs7O1lBR2pCO1lBQ0FDLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0lELGdCQUNBQztnQkFEdkIsTUFBTW5CLFFBQVEsSUFBSXJLLEtBQUt1TCxDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHFDQUFBQSxlQUFnQnhMLElBQUksS0FBSSxHQUFHdUssT0FBTztnQkFDekQsTUFBTUMsUUFBUSxJQUFJdkssS0FBS3dMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEkscUNBQUFBLGVBQWdCekwsSUFBSSxLQUFJLEdBQUd1SyxPQUFPO2dCQUV6RCxPQUFPQyxRQUFRRjtZQUNuQjtRQUNKO1FBQ0E7WUFDSVUsYUFBYTtZQUNiVSxlQUFlO1lBQ2ZULFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUM1SyxvRkFBbUJBO29CQUNoQjRLLFFBQVFBO29CQUNSMUIsT0FBTTs7Ozs7OztZQUdkbUMsWUFBWTtZQUNaUixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU03QixXQUFXNkIsSUFBSUMsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUN6Syw4Q0FBQ0E7OEJBQ0cySSxTQUFTbEUsYUFBYSxDQUFDZCxLQUFLLEdBQ3ZCZ0YsU0FBU2xFLGFBQWEsQ0FBQ2QsS0FBSyxDQUFDRSxHQUFHLENBQUMsQ0FBQ0MscUJBQzlCLDhEQUFDa0g7O2dDQUFvQmxILEtBQUs4RSxLQUFLO2dDQUFDOzsyQkFBckI5RSxLQUFLRyxFQUFFOzs7O3lDQUV0Qjs7Ozs7O1lBR2xCO1lBQ0EwRyxXQUFXLENBQUNDLE1BQVdDO29CQUVmRCxxQ0FBQUEsb0NBQUFBLDhCQUFBQSxnQkFFQUMscUNBQUFBLG9DQUFBQSw4QkFBQUE7Z0JBSEosTUFBTUksU0FDRkwsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEsK0JBQUFBLGVBQWdCbkcsYUFBYSxjQUE3Qm1HLG9EQUFBQSxxQ0FBQUEsNkJBQStCakgsS0FBSyxjQUFwQ2lILDBEQUFBQSxzQ0FBQUEsa0NBQXNDLENBQUMsRUFBRSxjQUF6Q0EsMERBQUFBLG9DQUEyQ2hDLEtBQUssS0FBSTtnQkFDeEQsTUFBTXNDLFNBQ0ZMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEksc0NBQUFBLCtCQUFBQSxlQUFnQnBHLGFBQWEsY0FBN0JvRyxvREFBQUEscUNBQUFBLDZCQUErQmxILEtBQUssY0FBcENrSCwwREFBQUEsc0NBQUFBLGtDQUFzQyxDQUFDLEVBQUUsY0FBekNBLDBEQUFBQSxvQ0FBMkNqQyxLQUFLLEtBQUk7Z0JBQ3hELE9BQU9xQyxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJZCxhQUFhO1lBQ2JVLGVBQWU7WUFDZlQsUUFBUTtvQkFBQyxFQUFFQyxNQUFNLEVBQW1CO3FDQUNoQyw4REFBQzVLLG9GQUFtQkE7b0JBQUM0SyxRQUFRQTtvQkFBUTFCLE9BQU07Ozs7Ozs7WUFFL0NtQyxZQUFZO1lBQ1pSLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFNWDdCO2dCQUxiLE1BQU1BLFdBQVc2QixJQUFJQyxRQUFRO2dCQUU3QixxQkFDSSw4REFBQ1c7b0JBQUlDLFdBQVU7O3NDQUNYLDhEQUFDTDs0QkFBS0ssV0FBVTtzQ0FDWDFDLEVBQUFBLG1CQUFBQSxTQUFTM0UsTUFBTSxjQUFmMkUsdUNBQUFBLGlCQUFpQkMsS0FBSyxLQUNuQkQsU0FBU2pCLG9CQUFvQixJQUM3Qjs7Ozs7O3NDQUVSLDhEQUFDbEgseURBQWFBOzRCQUNWd0QsUUFBUTJFLFNBQVMzRSxNQUFNOzRCQUN2QnNILGVBQWM7Ozs7Ozs7Ozs7OztZQUk5QjtZQUVBWCxXQUFXLENBQUNDLE1BQVdDO29CQUNKRCx1QkFBQUEsZ0JBQ0FDLHVCQUFBQTtnQkFEZixNQUFNSSxTQUFTTCxDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHNDQUFBQSx3QkFBQUEsZUFBZ0I1RyxNQUFNLGNBQXRCNEcsNENBQUFBLHNCQUF3QmhDLEtBQUssS0FBSTtnQkFDaEQsTUFBTXNDLFNBQVNMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEksc0NBQUFBLHdCQUFBQSxlQUFnQjdHLE1BQU0sY0FBdEI2Ryw0Q0FBQUEsc0JBQXdCakMsS0FBSyxLQUFJO2dCQUNoRCxPQUFPcUMsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSWQsYUFBYTtZQUNiVSxlQUFlO1lBQ2ZULFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUM1SyxvRkFBbUJBO29CQUFDNEssUUFBUUE7b0JBQVExQixPQUFNOzs7Ozs7O1lBRS9DbUMsWUFBWTtZQUNaUixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU03QixXQUFXNkIsSUFBSUMsUUFBUTtvQkFpQlI5QiwyQkFpQkFBO2dCQWpDckIscUJBQ0ksOERBQUN5QztvQkFBSUMsV0FBVTs4QkFDVixDQUFDOUksNkJBQ0UsOERBQUN0QyxvREFBT0E7OzBDQUNKLDhEQUFDRSwyREFBY0E7MENBQ1gsNEVBQUNQLG1EQUFNQTtvQ0FBQzJMLE1BQUs7b0NBQUtDLFNBQVM7OENBQ3ZCLDRFQUFDM0wsMkRBQWNBO3dDQUFDd0wsV0FBVTtrREFDckJ0TCxnRUFBZUEsQ0FDWjRJLFNBQVNFLE9BQU8sQ0FBQ1IsU0FBUyxFQUMxQk0sU0FBU0UsT0FBTyxDQUFDUCxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7OzBDQUt4Qyw4REFBQ3BJLDJEQUFjQTs7b0NBQ1Z5SSxTQUFTRSxPQUFPLENBQUNSLFNBQVM7b0NBQUU7b0NBQzVCTSxDQUFBQSw0QkFBQUEsU0FBU0UsT0FBTyxDQUFDUCxPQUFPLGNBQXhCSyx1Q0FBQUEsNEJBQTRCOzs7Ozs7Ozs7Ozs7a0RBSXJDLDhEQUFDMUksb0RBQU9BOzswQ0FDSiw4REFBQ0UsMkRBQWNBOzBDQUNYLDRFQUFDUCxtREFBTUE7b0NBQUMyTCxNQUFLO29DQUFLQyxTQUFTOzhDQUN2Qiw0RUFBQzNMLDJEQUFjQTt3Q0FBQ3dMLFdBQVU7a0RBQ3JCdEwsZ0VBQWVBLENBQ1o0SSxTQUFTRSxPQUFPLENBQUNSLFNBQVMsRUFDMUJNLFNBQVNFLE9BQU8sQ0FBQ1AsT0FBTzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLeEMsOERBQUNwSSwyREFBY0E7O29DQUNWeUksU0FBU0UsT0FBTyxDQUFDUixTQUFTO29DQUFFO29DQUM1Qk0sQ0FBQUEsNkJBQUFBLFNBQVNFLE9BQU8sQ0FBQ1AsT0FBTyxjQUF4Qkssd0NBQUFBLDZCQUE0Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTXJEO1lBQ0FnQyxXQUFXLENBQUNDLE1BQVdDO29CQUVaRCxnQkFBMkJBLHdCQUFBQSxpQkFBc0NBLGlCQUEyQkEseUJBQUFBLGlCQUc1RkMsZ0JBQTJCQSx3QkFBQUEsaUJBQXNDQSxpQkFBMkJBLHlCQUFBQTtnQkFKbkcsTUFBTUksU0FDRixHQUFvRUwsT0FBakVBLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcscUNBQUFBLGVBQWdCL0IsT0FBTyxNQUFJK0IsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEseUJBQUFBLGdCQUFnQi9CLE9BQU8sY0FBdkIrQiw2Q0FBQUEsdUJBQXlCdkMsU0FBUyxHQUFDLEtBQStELE9BQTVEdUMsQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEsZ0JBQWdCL0IsT0FBTyxNQUFJK0IsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyx1Q0FBQUEsMEJBQUFBLGdCQUFnQi9CLE9BQU8sY0FBdkIrQiw4Q0FBQUEsd0JBQXlCdEMsT0FBTyxNQUMvSDtnQkFDSixNQUFNNEMsU0FDRixHQUFvRUwsT0FBakVBLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEkscUNBQUFBLGVBQWdCaEMsT0FBTyxNQUFJZ0MsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSx1Q0FBQUEseUJBQUFBLGdCQUFnQmhDLE9BQU8sY0FBdkJnQyw2Q0FBQUEsdUJBQXlCeEMsU0FBUyxHQUFDLEtBQStELE9BQTVEd0MsQ0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxzQ0FBQUEsZ0JBQWdCaEMsT0FBTyxNQUFJZ0MsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSx1Q0FBQUEsMEJBQUFBLGdCQUFnQmhDLE9BQU8sY0FBdkJnQyw4Q0FBQUEsd0JBQXlCdkMsT0FBTyxNQUMvSDtnQkFDSixPQUFPMkMsT0FBT0UsYUFBYSxDQUFDRDtZQUNoQztRQUNKO1FBQ0E7WUFDSWQsYUFBYTtZQUNiVSxlQUFlO1lBQ2ZULFFBQVE7b0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtxQ0FDaEMsOERBQUM1SyxvRkFBbUJBO29CQUFDNEssUUFBUUE7b0JBQVExQixPQUFNOzs7Ozs7O1lBRS9DbUMsWUFBWTtZQUNaUixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7Z0JBQ3hCLE1BQU03QixXQUFXNkIsSUFBSUMsUUFBUTtnQkFDN0IscUJBQ0ksOERBQUNXO29CQUFJQyxXQUFVOzhCQUNWMUMsU0FBUzdELE9BQU8sQ0FBQ25CLEtBQUssQ0FBQ0UsR0FBRyxDQUN2QixDQUFDZ0UsUUFBYTREOzRCQWlCRzVEO3dCQWhCYixxQkFDSSw4REFBQzVILG9EQUFPQTs7OENBQ0osOERBQUNFLDJEQUFjQTs4Q0FDWCw0RUFBQ1AsbURBQU1BO3dDQUNIMkwsTUFBSzt3Q0FDTEMsU0FBUztrREFDVCw0RUFBQzNMLDJEQUFjQTs0Q0FBQ3dMLFdBQVU7c0RBQ3JCdEwsZ0VBQWVBLENBQ1o4SCxPQUFPUSxTQUFTLEVBQ2hCUixPQUFPUyxPQUFPOzs7Ozs7Ozs7Ozs7Ozs7OzhDQUs5Qiw4REFBQ3BJLDJEQUFjQTs7d0NBQ1YySCxPQUFPUSxTQUFTO3dDQUFFO3dDQUNsQlIsQ0FBQUEsa0JBQUFBLE9BQU9TLE9BQU8sY0FBZFQsNkJBQUFBLGtCQUFrQjs7Ozs7Ozs7MkJBZmI0RDs7Ozs7b0JBbUJ0Qjs7Ozs7O1lBSWhCO1lBQ0FkLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBRVpELCtCQUFBQSw4QkFBQUEsd0JBQUFBLGdCQUF3REEsZ0NBQUFBLCtCQUFBQSx5QkFBQUEsaUJBR3hEQywrQkFBQUEsOEJBQUFBLHdCQUFBQSxnQkFBd0RBLGdDQUFBQSwrQkFBQUEseUJBQUFBO29CQUh4REQseUNBQXdEQTtnQkFEL0QsTUFBTUssU0FDRixHQUEyREwsT0FBeERBLENBQUFBLDBDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHNDQUFBQSx5QkFBQUEsZUFBZ0I5RixPQUFPLGNBQXZCOEYsOENBQUFBLCtCQUFBQSx1QkFBeUJqSCxLQUFLLGNBQTlCaUgsb0RBQUFBLGdDQUFBQSw0QkFBZ0MsQ0FBQyxFQUFFLGNBQW5DQSxvREFBQUEsOEJBQXFDdkMsU0FBUyxjQUE5Q3VDLHFEQUFBQSwwQ0FBa0QsSUFBRyxLQUFzRCxPQUFuREEsQ0FBQUEsd0NBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1ILFFBQVEsY0FBZEcsdUNBQUFBLDBCQUFBQSxnQkFBZ0I5RixPQUFPLGNBQXZCOEYsK0NBQUFBLGdDQUFBQSx3QkFBeUJqSCxLQUFLLGNBQTlCaUgscURBQUFBLGlDQUFBQSw2QkFBZ0MsQ0FBQyxFQUFFLGNBQW5DQSxxREFBQUEsK0JBQXFDdEMsT0FBTyxjQUE1Q3NDLG1EQUFBQSx3Q0FBZ0QsT0FDM0c7b0JBRUdDLHlDQUF3REE7Z0JBRC9ELE1BQU1LLFNBQ0YsR0FBMkRMLE9BQXhEQSxDQUFBQSwwQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxzQ0FBQUEseUJBQUFBLGVBQWdCL0YsT0FBTyxjQUF2QitGLDhDQUFBQSwrQkFBQUEsdUJBQXlCbEgsS0FBSyxjQUE5QmtILG9EQUFBQSxnQ0FBQUEsNEJBQWdDLENBQUMsRUFBRSxjQUFuQ0Esb0RBQUFBLDhCQUFxQ3hDLFNBQVMsY0FBOUN3QyxxREFBQUEsMENBQWtELElBQUcsS0FBc0QsT0FBbkRBLENBQUFBLHdDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNSixRQUFRLGNBQWRJLHVDQUFBQSwwQkFBQUEsZ0JBQWdCL0YsT0FBTyxjQUF2QitGLCtDQUFBQSxnQ0FBQUEsd0JBQXlCbEgsS0FBSyxjQUE5QmtILHFEQUFBQSxpQ0FBQUEsNkJBQWdDLENBQUMsRUFBRSxjQUFuQ0EscURBQUFBLCtCQUFxQ3ZDLE9BQU8sY0FBNUN1QyxtREFBQUEsd0NBQWdELE9BQzNHO2dCQUNKLE9BQU9JLE9BQU9FLGFBQWEsQ0FBQ0Q7WUFDaEM7UUFDSjtLQUNIO0lBRUQscUJBQ0ksOERBQUNFO1FBQUlDLFdBQVU7OzBCQUNYLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDWCw0RUFBQ3ZMLGlEQUFJQTs4QkFDRCw0RUFBQ2pCLGtFQUFrQkE7d0JBQ2YrQixVQUFVQTt3QkFDVjhLLFVBQVV0Rjt3QkFDVjVELGlCQUFpQixDQUFDTTt3QkFDbEIvQixnQkFBZ0JBOzs7Ozs7Ozs7Ozs7Ozs7O1lBSTNCQSxlQUFlK0ksUUFBUSxDQUFDLG1CQUNyQkQscUNBQ0ksOERBQUN1QjtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUMxTCxvRkFBT0E7d0JBQUMwTCxXQUFVOzs7Ozs7b0JBQThCOzs7Ozs7MENBSXJELDhEQUFDTTtnQkFDR2hLLHFCQUFxQm9JO2dCQUNyQjZCLGdCQUFnQjtnQkFDaEJDLFVBQVU7Ozs7OzRCQUdsQi9JLGlCQUNBK0QsMkNBQ0ksOERBQUN1RTtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUMxTCxvRkFBT0E7d0JBQUMwTCxXQUFVOzs7Ozs7b0JBQThCOzs7Ozs7MENBSXJELDhEQUFDTTtnQkFDR2hLLHFCQUFxQkE7Ozs7OzBDQUk3QjswQkFDS0YsQ0FBQUEseUJBQUFBLG1DQUFBQSxhQUFjcUssTUFBTSxJQUFHLGtCQUNwQiw4REFBQ3JNLGdFQUFTQTtvQkFDTjBLLFNBQVNBO29CQUNUMUcsTUFBTWhDO29CQUNOb0ssVUFBVTtvQkFDVkgsVUFBVXRGO29CQUNWMkYsYUFBYTs7Ozs7OENBR2pCLDhEQUFDWDtvQkFBSUMsV0FBWTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ1gsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDWCw4REFBQ1c7b0NBQ0dYLFdBQVU7b0NBQ1ZZLE9BQU07b0NBQ05DLFNBQVE7O3NEQUNSLDhEQUFDQzs0Q0FDR0MsR0FBRTs0Q0FDRkMsTUFBSzs0Q0FDTEMsYUFBWTs7Ozs7O3NEQUVoQiw4REFBQ0g7NENBQ0dDLEdBQUU7NENBQ0ZDLE1BQUs7NENBQ0xFLFVBQVM7NENBQ1RDLFNBQVE7NENBQ1JGLGFBQVk7Ozs7OztzREFFaEIsOERBQUNIOzRDQUNHQyxHQUFFOzRDQUNGQyxNQUFLOzRDQUNMQyxhQUFZOzs7Ozs7c0RBRWhCLDhEQUFDSDs0Q0FDR0MsR0FBRTs0Q0FDRkMsTUFBSzs0Q0FDTEMsYUFBWTs7Ozs7O3NEQUVoQiw4REFBQ0g7NENBQ0dDLEdBQUU7NENBQ0ZDLE1BQUs7NENBQ0xDLGFBQVk7Ozs7OztzREFFaEIsOERBQUNIOzRDQUNHQyxHQUFFOzRDQUNGQyxNQUFLOzRDQUNMQyxhQUFZOzs7Ozs7c0RBRWhCLDhEQUFDSDs0Q0FDR0MsR0FBRTs0Q0FDRkMsTUFBSzs0Q0FDTEMsYUFBWTs7Ozs7O3NEQUVoQiw4REFBQ0g7NENBQ0dDLEdBQUU7NENBQ0ZDLE1BQUs7NENBQ0xDLGFBQVk7Ozs7OztzREFFaEIsOERBQUNIOzRDQUNHQyxHQUFFOzRDQUNGQyxNQUFLOzRDQUNMQyxhQUFZOzs7Ozs7c0RBRWhCLDhEQUFDSDs0Q0FDR0MsR0FBRTs0Q0FDRkMsTUFBSzs0Q0FDTEMsYUFBWTs7Ozs7O3NEQUVoQiw4REFBQ0g7NENBQ0dDLEdBQUU7NENBQ0ZDLE1BQUs7NENBQ0xDLGFBQVk7Ozs7OztzREFFaEIsOERBQUNIOzRDQUNHQyxHQUFFOzRDQUNGQyxNQUFLOzRDQUNMQyxhQUFZOzs7Ozs7c0RBRWhCLDhEQUFDSDs0Q0FDR0MsR0FBRTs0Q0FDRkMsTUFBSzs0Q0FDTEMsYUFBWTs7Ozs7O3NEQUVoQiw4REFBQ0g7NENBQ0dDLEdBQUU7NENBQ0ZDLE1BQUs7NENBQ0xDLGFBQVk7Ozs7OztzREFFaEIsOERBQUNIOzRDQUNHQyxHQUFFOzRDQUNGQyxNQUFLOzRDQUNMQyxhQUFZOzs7Ozs7Ozs7Ozs7OENBR3BCLDhEQUFDRztvQ0FBRXBCLFdBQVU7OENBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBV3pDLENBQUN0SyxlQUFlK0ksUUFBUSxDQUFDLGtDQUN0Qiw4REFBQzRDO2dCQUNHckIsV0FBVTtnQkFDVnNCLFNBQVMsSUFBTTNKLHFCQUFxQixDQUFDRSxPQUFTLENBQUNBOzBCQUM5Q0osaUJBQ0ssaUNBQ0E7Ozs7Ozs7Ozs7OztBQUsxQjtHQTN1Qk1uQzs7UUFXYXBCLHNEQUFTQTtRQW1Cc0JnQixnREFBYUE7UUFDNUNILDJEQUFhQTtRQUV4QkssMkVBQWlCQTtRQTJCeUNoQyx5REFBWUE7UUE2R3RFNkIsMEVBQWtCQTtRQWlCbEI3Qix5REFBWUE7OztLQTFMZGtDO0FBNnVCTiwrREFBZUEsZ0JBQWdCQSxFQUFBO0FBRXhCLE1BQU1nTCxzQkFBc0I7UUFBQyxFQUNoQ2hLLG1CQUFtQixFQUNuQlksZUFBZSxLQUFLLEVBQ3BCcUosaUJBQWlCLEtBQUssRUFDdEJDLFdBQVcsRUFBRSxFQUNYOztJQUNGLE1BQU1uSixTQUFTdEMsK0RBQWFBLENBQUM7SUFFN0IsTUFBTXdNLGFBQWFwTix3RUFBYUEsQ0FBQztRQUM3QjtZQUNJNEssYUFBYTtZQUNiQyxRQUFRO1lBQ1JFLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtnQkFDeEIsTUFBTS9HLE9BQVkrRyxJQUFJQyxRQUFRO2dCQUM5QixxQkFBTyw4REFBQy9KLGlGQUFrQkE7b0JBQUMrQyxNQUFNQTtvQkFBTWlILE1BQUs7Ozs7OztZQUNoRDtRQUNKO1FBQ0E7WUFDSU4sYUFBYTtZQUNiVSxlQUFlO1lBQ2ZULFFBQVE7WUFDUlUsWUFBWTtZQUNaUixNQUFNO29CQUFDLEVBQUVDLEdBQUcsRUFBZ0I7b0JBTVByRDtnQkFMakIsTUFBTUEsTUFBTXFELElBQUlDLFFBQVE7Z0JBQ3hCLHFCQUNJOzhCQUNLbEksZ0JBQWdCLHVCQUNiLDhEQUFDNkk7d0JBQUlDLFdBQVU7a0NBQ1ZsRSxFQUFBQSxjQUFBQSxJQUFJbkQsTUFBTSxjQUFWbUQsa0NBQUFBLFlBQVl5QixLQUFLLEtBQUk7Ozs7Ozs7WUFLMUM7WUFDQStCLFdBQVcsQ0FBQ0MsTUFBV0M7b0JBQ0pELHVCQUFBQSxnQkFDQUMsdUJBQUFBO2dCQURmLE1BQU1JLFNBQVNMLENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1ILFFBQVEsY0FBZEcsc0NBQUFBLHdCQUFBQSxlQUFnQjVHLE1BQU0sY0FBdEI0Ryw0Q0FBQUEsc0JBQXdCaEMsS0FBSyxLQUFJO2dCQUNoRCxNQUFNc0MsU0FBU0wsQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUosUUFBUSxjQUFkSSxzQ0FBQUEsd0JBQUFBLGVBQWdCN0csTUFBTSxjQUF0QjZHLDRDQUFBQSxzQkFBd0JqQyxLQUFLLEtBQUk7Z0JBQ2hELE9BQU9xQyxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJZCxhQUFhO1lBQ2JVLGVBQWU7WUFDZlQsUUFBUTtZQUNSVSxZQUFZO1lBQ1pSLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFxQ1pyRDtnQkFwQ1osTUFBTUEsTUFBTXFELElBQUlDLFFBQVE7Z0JBQ3hCLE1BQU0zRixVQUFVcUMsSUFBSXJDLE9BQU8sSUFBSSxFQUFFO2dCQUVqQyxPQUFPcEMsdUJBQ0gsOERBQUMwSTtvQkFBSUMsV0FBVTs4QkFDVnZHLFFBQVFqQixHQUFHLENBQUMsQ0FBQ2dFOzRCQXFCR0E7d0JBcEJiLHFCQUNJLDhEQUFDNUgsb0RBQU9BOzs4Q0FDSiw4REFBQ0UsMkRBQWNBOzhDQUNYLDRFQUFDUCxtREFBTUE7d0NBQ0gyTCxNQUFLO3dDQUNMQyxTQUNJckUsSUFBSUMsTUFBTSxDQUFDMkIsU0FBUyxHQUNkLGdCQUNBO2tEQUVWLDRFQUFDbEosMkRBQWNBOzRDQUFDd0wsV0FBVTtzREFDckJ0TCxnRUFBZUEsQ0FDWjhILE9BQU9RLFNBQVMsRUFDaEJSLE9BQU9TLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OENBSzlCLDhEQUFDcEksMkRBQWNBOzt3Q0FDVjJILE9BQU9RLFNBQVM7d0NBQUU7d0NBQ2xCUixDQUFBQSxrQkFBQUEsT0FBT1MsT0FBTyxjQUFkVCw2QkFBQUEsa0JBQWtCOzs7Ozs7OzsyQkFuQmJBLE9BQU81RCxFQUFFOzs7OztvQkF1Qi9COzs7Ozs4Q0FHSiw4REFBQ21IO29CQUNHQyxXQUFXaEwsbURBQUVBLENBQ1QsMEJBQ0E4RyxjQUFBQSxJQUFJQyxNQUFNLGNBQVZELGtDQUFBQSxZQUFZNkIsS0FBSzs4QkFFcEJsRSxRQUFRZ0gsTUFBTTs7Ozs7O1lBRzNCO1lBQ0FuQixXQUFXLENBQUNDLE1BQVdDO29CQUVaRCwrQkFBQUEsOEJBQUFBLHdCQUFBQSxnQkFBd0RBLGdDQUFBQSwrQkFBQUEseUJBQUFBLGlCQUd4REMsK0JBQUFBLDhCQUFBQSx3QkFBQUEsZ0JBQXdEQSxnQ0FBQUEsK0JBQUFBLHlCQUFBQTtvQkFIeERELHlDQUF3REE7Z0JBRC9ELE1BQU1LLFNBQ0YsR0FBMkRMLE9BQXhEQSxDQUFBQSwwQ0FBQUEsaUJBQUFBLDRCQUFBQSxpQkFBQUEsS0FBTUgsUUFBUSxjQUFkRyxzQ0FBQUEseUJBQUFBLGVBQWdCOUYsT0FBTyxjQUF2QjhGLDhDQUFBQSwrQkFBQUEsdUJBQXlCakgsS0FBSyxjQUE5QmlILG9EQUFBQSxnQ0FBQUEsNEJBQWdDLENBQUMsRUFBRSxjQUFuQ0Esb0RBQUFBLDhCQUFxQ3ZDLFNBQVMsY0FBOUN1QyxxREFBQUEsMENBQWtELElBQUcsS0FBc0QsT0FBbkRBLENBQUFBLHdDQUFBQSxpQkFBQUEsNEJBQUFBLGtCQUFBQSxLQUFNSCxRQUFRLGNBQWRHLHVDQUFBQSwwQkFBQUEsZ0JBQWdCOUYsT0FBTyxjQUF2QjhGLCtDQUFBQSxnQ0FBQUEsd0JBQXlCakgsS0FBSyxjQUE5QmlILHFEQUFBQSxpQ0FBQUEsNkJBQWdDLENBQUMsRUFBRSxjQUFuQ0EscURBQUFBLCtCQUFxQ3RDLE9BQU8sY0FBNUNzQyxtREFBQUEsd0NBQWdELE9BQzNHO29CQUVHQyx5Q0FBd0RBO2dCQUQvRCxNQUFNSyxTQUNGLEdBQTJETCxPQUF4REEsQ0FBQUEsMENBQUFBLGlCQUFBQSw0QkFBQUEsaUJBQUFBLEtBQU1KLFFBQVEsY0FBZEksc0NBQUFBLHlCQUFBQSxlQUFnQi9GLE9BQU8sY0FBdkIrRiw4Q0FBQUEsK0JBQUFBLHVCQUF5QmxILEtBQUssY0FBOUJrSCxvREFBQUEsZ0NBQUFBLDRCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLG9EQUFBQSw4QkFBcUN4QyxTQUFTLGNBQTlDd0MscURBQUFBLDBDQUFrRCxJQUFHLEtBQXNELE9BQW5EQSxDQUFBQSx3Q0FBQUEsaUJBQUFBLDRCQUFBQSxrQkFBQUEsS0FBTUosUUFBUSxjQUFkSSx1Q0FBQUEsMEJBQUFBLGdCQUFnQi9GLE9BQU8sY0FBdkIrRiwrQ0FBQUEsZ0NBQUFBLHdCQUF5QmxILEtBQUssY0FBOUJrSCxxREFBQUEsaUNBQUFBLDZCQUFnQyxDQUFDLEVBQUUsY0FBbkNBLHFEQUFBQSwrQkFBcUN2QyxPQUFPLGNBQTVDdUMsbURBQUFBLHdDQUFnRCxPQUMzRztnQkFDSixPQUFPSSxPQUFPRSxhQUFhLENBQUNEO1lBQ2hDO1FBQ0o7UUFDQTtZQUNJZCxhQUFhO1lBQ2JVLGVBQWU7WUFDZlQsUUFBUTtZQUNSVSxZQUFZO1lBQ1pSLE1BQU07b0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtvQkFJRnJELGFBQXdCQSxjQUNyQ0E7Z0JBSlQsTUFBTUEsTUFBTXFELElBQUlDLFFBQVE7Z0JBQ3hCLHFCQUNJLDhEQUFDVztvQkFDR0MsV0FBVyxHQUFrRCxPQUEvQ2xFLEVBQUFBLGNBQUFBLElBQUlDLE1BQU0sY0FBVkQsa0NBQUFBLFlBQVk0QixTQUFTLEtBQUc1QixlQUFBQSxJQUFJQyxNQUFNLGNBQVZELG1DQUFBQSxhQUFZNkIsS0FBSyxHQUFHLElBQUc7OEJBQzVEN0IsRUFBQUEsZUFBQUEsSUFBSUMsTUFBTSxjQUFWRCxtQ0FBQUEsYUFBWTJCLEtBQUssS0FBSTs7Ozs7O1lBR2xDO1FBQ0o7S0FDSDtJQUVELHFEQUFxRDtJQUNyRCxNQUFNcUIsVUFBVXlCLGlCQUNWZ0IsV0FBV3RJLE1BQU0sQ0FBQyxDQUFDdUksTUFBYUEsSUFBSXpDLFdBQVcsS0FBSyxVQUNwRHdDO0lBRU4scUJBQ0k7a0JBQ0tqTCxDQUFBQSxnQ0FBQUEsMENBQUFBLG9CQUFxQm1LLE1BQU0sSUFBRyxrQkFDM0IsOERBQUNyTSxnRUFBU0E7WUFDTjBLLFNBQVNBO1lBQ1QxRyxNQUFNOUI7WUFDTmtLLFVBQVVBO1lBQ1ZFLGFBQWE7Ozs7O3NDQUdqQiw4REFBQ1g7WUFBSUMsV0FBWTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ1gsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FDWCw4REFBQ1c7NEJBQ0dYLFdBQVU7NEJBQ1ZZLE9BQU07NEJBQ05DLFNBQVE7OzhDQUNSLDhEQUFDQztvQ0FDR0MsR0FBRTtvQ0FDRkMsTUFBSztvQ0FDTEMsYUFBWTs7Ozs7OzhDQUVoQiw4REFBQ0g7b0NBQ0dDLEdBQUU7b0NBQ0ZDLE1BQUs7b0NBQ0xFLFVBQVM7b0NBQ1RDLFNBQVE7b0NBQ1JGLGFBQVk7Ozs7Ozs4Q0FFaEIsOERBQUNIO29DQUNHQyxHQUFFO29DQUNGQyxNQUFLO29DQUNMQyxhQUFZOzs7Ozs7OENBRWhCLDhEQUFDSDtvQ0FDR0MsR0FBRTtvQ0FDRkMsTUFBSztvQ0FDTEMsYUFBWTs7Ozs7OzhDQUVoQiw4REFBQ0g7b0NBQ0dDLEdBQUU7b0NBQ0ZDLE1BQUs7b0NBQ0xDLGFBQVk7Ozs7Ozs4Q0FFaEIsOERBQUNIO29DQUNHQyxHQUFFO29DQUNGQyxNQUFLO29DQUNMQyxhQUFZOzs7Ozs7OENBRWhCLDhEQUFDSDtvQ0FDR0MsR0FBRTtvQ0FDRkMsTUFBSztvQ0FDTEMsYUFBWTs7Ozs7OzhDQUVoQiw4REFBQ0g7b0NBQ0dDLEdBQUU7b0NBQ0ZDLE1BQUs7b0NBQ0xDLGFBQVk7Ozs7Ozs4Q0FFaEIsOERBQUNIO29DQUNHQyxHQUFFO29DQUNGQyxNQUFLO29DQUNMQyxhQUFZOzs7Ozs7OENBRWhCLDhEQUFDSDtvQ0FDR0MsR0FBRTtvQ0FDRkMsTUFBSztvQ0FDTEMsYUFBWTs7Ozs7OzhDQUVoQiw4REFBQ0g7b0NBQ0dDLEdBQUU7b0NBQ0ZDLE1BQUs7b0NBQ0xDLGFBQVk7Ozs7Ozs4Q0FFaEIsOERBQUNIO29DQUNHQyxHQUFFO29DQUNGQyxNQUFLO29DQUNMQyxhQUFZOzs7Ozs7OENBRWhCLDhEQUFDSDtvQ0FDR0MsR0FBRTtvQ0FDRkMsTUFBSztvQ0FDTEMsYUFBWTs7Ozs7OzhDQUVoQiw4REFBQ0g7b0NBQ0dDLEdBQUU7b0NBQ0ZDLE1BQUs7b0NBQ0xDLGFBQVk7Ozs7Ozs4Q0FFaEIsOERBQUNIO29DQUNHQyxHQUFFO29DQUNGQyxNQUFLO29DQUNMQyxhQUFZOzs7Ozs7Ozs7Ozs7c0NBR3BCLDhEQUFDRzs0QkFBRXBCLFdBQVU7c0NBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVTlDLEVBQUM7SUFqT1lNOztRQU1NdkwsMkRBQWFBOzs7TUFObkJ1TCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL2NyZXctdHJhaW5pbmcvbGlzdC50c3g/NGU2OCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcclxuXHJcbmltcG9ydCB7XHJcbiAgICBUUkFJTklOR19TRVNTSU9OUyxcclxuICAgIFJFQURfVFJBSU5JTkdfU0VTU0lPTl9EVUVTLFxyXG59IGZyb20gJ0AvYXBwL2xpYi9ncmFwaFFML3F1ZXJ5J1xyXG5pbXBvcnQgeyB1c2VMYXp5UXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcclxuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xyXG5pbXBvcnQgeyBUcmFpbmluZ0xpc3RGaWx0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZmlsdGVyJ1xyXG5pbXBvcnQgeyBHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMgfSBmcm9tICdAL2FwcC9saWIvYWN0aW9ucydcclxuaW1wb3J0IHsgZm9ybWF0IH0gZnJvbSAnZGF0ZS1mbnMnXHJcbmltcG9ydCB7IGdldFBlcm1pc3Npb25zLCBoYXNQZXJtaXNzaW9uIH0gZnJvbSAnQC9hcHAvaGVscGVycy91c2VySGVscGVyJ1xyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGZvcm1hdCBkYXRlcyB1c2luZyBkYXRlLWZuc1xyXG5jb25zdCBmb3JtYXREYXRlID0gKGRhdGVTdHJpbmc6IGFueSkgPT4ge1xyXG4gICAgaWYgKCFkYXRlU3RyaW5nKSByZXR1cm4gJydcclxuICAgIHRyeSB7XHJcbiAgICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKGRhdGVTdHJpbmcpXHJcbiAgICAgICAgcmV0dXJuIGZvcm1hdChkYXRlLCAnZGQvTU0veXknKVxyXG4gICAgfSBjYXRjaCB7XHJcbiAgICAgICAgcmV0dXJuICcnXHJcbiAgICB9XHJcbn1cclxuaW1wb3J0IExvYWRpbmcgZnJvbSAnQC9hcHAvbG9hZGluZydcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xyXG5pbXBvcnQge1xyXG4gICAgY3JlYXRlQ29sdW1ucyxcclxuICAgIERhdGFUYWJsZSxcclxuICAgIEV4dGVuZGVkQ29sdW1uRGVmLFxyXG59IGZyb20gJ0AvY29tcG9uZW50cy9maWx0ZXJlZFRhYmxlJ1xyXG5pbXBvcnQgeyBEYXRhVGFibGVTb3J0SGVhZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL2RhdGEtdGFibGUtc29ydC1oZWFkZXInXHJcbmltcG9ydCB7IExvYWRlcjIgfSBmcm9tICdsdWNpZGUtcmVhY3QnXHJcbmltcG9ydCB7XHJcbiAgICBBdmF0YXIsXHJcbiAgICBBdmF0YXJGYWxsYmFjayxcclxuICAgIENhcmQsXHJcbiAgICBnZXRDcmV3SW5pdGlhbHMsXHJcbiAgICBQLFxyXG4gICAgVG9vbHRpcCxcclxuICAgIFRvb2x0aXBDb250ZW50LFxyXG4gICAgVG9vbHRpcFRyaWdnZXIsXHJcbn0gZnJvbSAnQC9jb21wb25lbnRzL3VpJ1xyXG5pbXBvcnQgeyB1c2VNZWRpYVF1ZXJ5IH0gZnJvbSAnQHJlYWN0dXNlcy9jb3JlJ1xyXG5pbXBvcnQgeyBjbiB9IGZyb20gJ0AvYXBwL2xpYi91dGlscydcclxuaW1wb3J0IHsgdXNlVHJhaW5pbmdGaWx0ZXJzIH0gZnJvbSAnLi9ob29rcy91c2VUcmFpbmluZ0ZpbHRlcnMnXHJcbmltcG9ydCB7IHVzZVF1ZXJ5U3RhdGUgfSBmcm9tICdudXFzJ1xyXG5pbXBvcnQgeyBMb2NhdGlvbk1vZGFsIH0gZnJvbSAnLi4vdmVzc2Vscy9saXN0J1xyXG5pbXBvcnQgeyB1c2VWZXNzZWxJY29uRGF0YSB9IGZyb20gJ0AvYXBwL2xpYi92ZXNzZWwtaWNvbi1oZWxwZXInXHJcblxyXG5pbXBvcnQgeyBNb2JpbGVUcmFpbmluZ0NhcmQgfSBmcm9tICcuL2NvbXBvbmVudHMvbW9iaWxlLXRyYWluaW5nLWNhcmQnXHJcblxyXG5jb25zdCBDcmV3VHJhaW5pbmdMaXN0ID0gKHtcclxuICAgIG1lbWJlcklkID0gMCxcclxuICAgIHZlc3NlbElkID0gMCxcclxuICAgIGFwcGx5RmlsdGVyUmVmLFxyXG4gICAgZXhjbHVkZUZpbHRlcnMgPSBbXSxcclxufToge1xyXG4gICAgbWVtYmVySWQ/OiBudW1iZXJcclxuICAgIHZlc3NlbElkPzogbnVtYmVyXHJcbiAgICBhcHBseUZpbHRlclJlZj86IGFueVxyXG4gICAgZXhjbHVkZUZpbHRlcnM/OiBzdHJpbmdbXVxyXG59KSA9PiB7XHJcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxyXG4gICAgY29uc3QgbGltaXQgPSAxMDBcclxuICAgIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxyXG4gICAgY29uc3QgW3BhZ2VJbmZvLCBzZXRQYWdlSW5mb10gPSB1c2VTdGF0ZSh7XHJcbiAgICAgICAgdG90YWxDb3VudDogMCxcclxuICAgICAgICBoYXNOZXh0UGFnZTogZmFsc2UsXHJcbiAgICAgICAgaGFzUHJldmlvdXNQYWdlOiBmYWxzZSxcclxuICAgIH0pXHJcbiAgICBjb25zdCBbdHJhaW5pbmdMaXN0LCBzZXRUcmFpbmluZ0xpc3RdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW3RyYWluaW5nU2Vzc2lvbkR1ZXMsIHNldFRyYWluaW5nU2Vzc2lvbkR1ZXNdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW3BhZ2UsIHNldFBhZ2VdID0gdXNlU3RhdGUoMClcclxuICAgIC8vIGNvbnN0IFtmaWx0ZXIsIHNldEZpbHRlcl0gPSB1c2VTdGF0ZSh7fSlcclxuICAgIGNvbnN0IFt2ZXNzZWxJZE9wdGlvbnMsIHNldFZlc3NlbElkT3B0aW9uc10gPSB1c2VTdGF0ZShbXSBhcyBhbnkpXHJcbiAgICBjb25zdCBbdHJhaW5pbmdUeXBlSWRPcHRpb25zLCBzZXRUcmFpbmluZ1R5cGVJZE9wdGlvbnNdID0gdXNlU3RhdGUoXHJcbiAgICAgICAgW10gYXMgYW55LFxyXG4gICAgKVxyXG4gICAgY29uc3QgW3RyYWluZXJJZE9wdGlvbnMsIHNldFRyYWluZXJJZE9wdGlvbnNdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW2NyZXdJZE9wdGlvbnMsIHNldENyZXdJZE9wdGlvbnNdID0gdXNlU3RhdGUoW10gYXMgYW55KVxyXG4gICAgY29uc3QgW2lzVmVzc2VsVmlld10gPSB1c2VTdGF0ZShmYWxzZSlcclxuICAgIGNvbnN0IFtvdmVyZHVlU3dpdGNoZXIsIHNldE92ZXJkdWVTd2l0Y2hlcl0gPSB1c2VRdWVyeVN0YXRlKCdvdmVyZHVlJylcclxuICAgIGNvbnN0IGlzV2lkZSA9IHVzZU1lZGlhUXVlcnkoJyhtaW4td2lkdGg6IDcyMHB4KScpXHJcbiAgICBjb25zdCB7IGdldFZlc3NlbFdpdGhJY29uLCBsb2FkaW5nOiB2ZXNzZWxEYXRhTG9hZGluZyB9ID1cclxuICAgICAgICB1c2VWZXNzZWxJY29uRGF0YSgpXHJcblxyXG4gICAgLy8gQ3JlYXRlIGEgYm9vbGVhbiBzdGF0ZSB3cmFwcGVyIGZvciB0aGUgdXNlVHJhaW5pbmdGaWx0ZXJzIGhvb2tcclxuICAgIGNvbnN0IFtvdmVyZHVlQm9vbGVhbiwgc2V0T3ZlcmR1ZUJvb2xlYW5dID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpXHJcblxyXG4gICAgLy8gU3luYyB0aGUgYm9vbGVhbiBzdGF0ZSB3aXRoIHRoZSBxdWVyeSBzdGF0ZVxyXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgICAgICBzZXRPdmVyZHVlQm9vbGVhbihvdmVyZHVlU3dpdGNoZXIgPT09ICd0cnVlJylcclxuICAgIH0sIFtvdmVyZHVlU3dpdGNoZXJdKVxyXG5cclxuICAgIC8vIENyZWF0ZSBhIHdyYXBwZXIgZnVuY3Rpb24gdGhhdCBjb252ZXJ0cyBib29sZWFuIHRvIHN0cmluZyBmb3IgdGhlIHF1ZXJ5IHN0YXRlXHJcbiAgICBjb25zdCB0b2dnbGVPdmVyZHVlV3JhcHBlciA9IHVzZUNhbGxiYWNrKFxyXG4gICAgICAgICh2YWx1ZTogYm9vbGVhbiB8ICgocHJldjogYm9vbGVhbikgPT4gYm9vbGVhbikpID0+IHtcclxuICAgICAgICAgICAgaWYgKHR5cGVvZiB2YWx1ZSA9PT0gJ2Z1bmN0aW9uJykge1xyXG4gICAgICAgICAgICAgICAgc2V0T3ZlcmR1ZUJvb2xlYW4oKHByZXYpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBuZXdWYWx1ZSA9IHZhbHVlKHByZXYpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0T3ZlcmR1ZVN3aXRjaGVyKG5ld1ZhbHVlID8gJ3RydWUnIDogJ2ZhbHNlJylcclxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gbmV3VmFsdWVcclxuICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBzZXRPdmVyZHVlQm9vbGVhbih2YWx1ZSlcclxuICAgICAgICAgICAgICAgIHNldE92ZXJkdWVTd2l0Y2hlcih2YWx1ZSA/ICd0cnVlJyA6ICdmYWxzZScpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9LFxyXG4gICAgICAgIFtzZXRPdmVyZHVlU3dpdGNoZXJdLFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IFtxdWVyeVRyYWluaW5nTGlzdCwgeyBsb2FkaW5nOiB0cmFpbmluZ0xpc3RMb2FkaW5nIH1dID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIFRSQUlOSU5HX1NFU1NJT05TLFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgZmV0Y2hQb2xpY3k6ICdjYWNoZS1hbmQtbmV0d29yaycsXHJcbiAgICAgICAgICAgIG9uQ29tcGxldGVkOiAocmVzcG9uc2U6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZGF0YSA9IHJlc3BvbnNlLnJlYWRUcmFpbmluZ1Nlc3Npb25zLm5vZGVzXHJcblxyXG4gICAgICAgICAgICAgICAgLy8gVHJhbnNmb3JtIHZlc3NlbCBkYXRhIHRvIGluY2x1ZGUgY29tcGxldGUgdmVzc2VsIGluZm9ybWF0aW9uIHdpdGggcG9zaXRpb25cclxuICAgICAgICAgICAgICAgIGNvbnN0IHRyYW5zZm9ybWVkRGF0YSA9IGRhdGEubWFwKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBjb21wbGV0ZVZlc3NlbERhdGEgPSBnZXRWZXNzZWxXaXRoSWNvbihcclxuICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS52ZXNzZWwuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGl0ZW0udmVzc2VsLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgLi4uaXRlbSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmVzc2VsOiBjb21wbGV0ZVZlc3NlbERhdGEsXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgfSlcclxuXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2ZXNzZWxJRHMgPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICAgICAgICAgIG5ldyBTZXQoZGF0YS5tYXAoKGl0ZW06IGFueSkgPT4gaXRlbS52ZXNzZWwuaWQpKSxcclxuICAgICAgICAgICAgICAgICkuZmlsdGVyKChpZDogYW55KSA9PiAraWQgIT09IDApXHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZ1R5cGVJRHMgPSBBcnJheS5mcm9tKFxyXG4gICAgICAgICAgICAgICAgICAgIG5ldyBTZXQoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRhdGEuZmxhdE1hcCgoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaXRlbS50cmFpbmluZ1R5cGVzLm5vZGVzLm1hcCgodDogYW55KSA9PiB0LmlkKSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgY29uc3QgdHJhaW5lcklEcyA9IEFycmF5LmZyb20oXHJcbiAgICAgICAgICAgICAgICAgICAgbmV3IFNldChkYXRhLm1hcCgoaXRlbTogYW55KSA9PiBpdGVtLnRyYWluZXJJRCkpLFxyXG4gICAgICAgICAgICAgICAgKS5maWx0ZXIoKGlkOiBhbnkpID0+ICtpZCAhPT0gMClcclxuICAgICAgICAgICAgICAgIGNvbnN0IG1lbWJlcklEcyA9IEFycmF5LmZyb20oXHJcbiAgICAgICAgICAgICAgICAgICAgbmV3IFNldChcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5mbGF0TWFwKChpdGVtOiBhbnkpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpdGVtLm1lbWJlcnMubm9kZXMubWFwKCh0OiBhbnkpID0+IHQuaWQpLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApLFxyXG4gICAgICAgICAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgaWYgKHRyYW5zZm9ybWVkRGF0YSkge1xyXG4gICAgICAgICAgICAgICAgICAgIHNldFRyYWluaW5nTGlzdCh0cmFuc2Zvcm1lZERhdGEpXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VmVzc2VsSWRPcHRpb25zKHZlc3NlbElEcylcclxuICAgICAgICAgICAgICAgICAgICBzZXRUcmFpbmluZ1R5cGVJZE9wdGlvbnModHJhaW5pbmdUeXBlSURzKVxyXG4gICAgICAgICAgICAgICAgICAgIHNldFRyYWluZXJJZE9wdGlvbnModHJhaW5lcklEcylcclxuICAgICAgICAgICAgICAgICAgICBzZXRDcmV3SWRPcHRpb25zKG1lbWJlcklEcylcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIHNldFBhZ2VJbmZvKHJlc3BvbnNlLnJlYWRUcmFpbmluZ1Nlc3Npb25zLnBhZ2VJbmZvKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigncXVlcnlUcmFpbmluZ0xpc3QgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IGxvYWRUcmFpbmluZ0xpc3QgPSBhc3luYyAoXHJcbiAgICAgICAgc3RhcnRQYWdlOiBudW1iZXIgPSAwLFxyXG4gICAgICAgIHNlYXJjaEZpbHRlcjogYW55ID0geyAuLi5maWx0ZXIgfSxcclxuICAgICkgPT4ge1xyXG4gICAgICAgIGF3YWl0IHF1ZXJ5VHJhaW5pbmdMaXN0KHtcclxuICAgICAgICAgICAgdmFyaWFibGVzOiB7XHJcbiAgICAgICAgICAgICAgICBmaWx0ZXI6IHNlYXJjaEZpbHRlcixcclxuICAgICAgICAgICAgICAgIG9mZnNldDogc3RhcnRQYWdlICogbGltaXQsXHJcbiAgICAgICAgICAgICAgICBsaW1pdDogbGltaXQsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyA9IGFzeW5jIChmaWx0ZXI6IGFueSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGR1ZUZpbHRlcjogYW55ID0ge31cclxuICAgICAgICBpZiAobWVtYmVySWQgPiAwKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci5tZW1iZXJJRCA9IHsgZXE6ICttZW1iZXJJZCB9XHJcbiAgICAgICAgfVxyXG4gICAgICAgIGlmICh2ZXNzZWxJZCA+IDApIHtcclxuICAgICAgICAgICAgZHVlRmlsdGVyLnZlc3NlbElEID0geyBlcTogK3Zlc3NlbElkIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGZpbHRlci52ZXNzZWxJRCkge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIudmVzc2VsSUQgPSBmaWx0ZXIudmVzc2VsSURcclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGZpbHRlci50cmFpbmluZ1R5cGVzKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci50cmFpbmluZ1R5cGVJRCA9IHsgZXE6IGZpbHRlci50cmFpbmluZ1R5cGVzLmlkLmNvbnRhaW5zIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGZpbHRlci5tZW1iZXJzKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci5tZW1iZXJJRCA9IHsgZXE6IGZpbHRlci5tZW1iZXJzLmlkLmNvbnRhaW5zIH1cclxuICAgICAgICB9XHJcbiAgICAgICAgaWYgKGZpbHRlci5kYXRlKSB7XHJcbiAgICAgICAgICAgIGR1ZUZpbHRlci5kdWVEYXRlID0gZmlsdGVyLmRhdGVcclxuICAgICAgICB9IGVsc2Uge1xyXG4gICAgICAgICAgICBkdWVGaWx0ZXIuZHVlRGF0ZSA9IHsgbmU6IG51bGwgfVxyXG4gICAgICAgIH1cclxuICAgICAgICBhd2FpdCByZWFkVHJhaW5pbmdTZXNzaW9uRHVlcyh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyOiBkdWVGaWx0ZXIsXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBoYW5kbGVOYXZpZ2F0aW9uQ2xpY2sgPSAobmV3UGFnZTogYW55KSA9PiB7XHJcbiAgICAgICAgaWYgKG5ld1BhZ2UgPCAwIHx8IG5ld1BhZ2UgPT09IHBhZ2UpIHJldHVyblxyXG4gICAgICAgIHNldFBhZ2UobmV3UGFnZSlcclxuICAgICAgICBsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyhmaWx0ZXIpXHJcbiAgICAgICAgbG9hZFRyYWluaW5nTGlzdChuZXdQYWdlLCBmaWx0ZXIpXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3Qge1xyXG4gICAgICAgIGZpbHRlcixcclxuICAgICAgICBzZXRGaWx0ZXIsIC8vIHVzZWQgaW4gdGhlIHN0YXJ0dXAgZWZmZWN0XHJcbiAgICAgICAgaGFuZGxlRmlsdGVyQ2hhbmdlLCAvLyByZXBsYWNlcyB0aGUgb2xkIGhhbmRsZUZpbHRlck9uQ2hhbmdlXHJcbiAgICB9ID0gdXNlVHJhaW5pbmdGaWx0ZXJzKHtcclxuICAgICAgICBpbml0aWFsRmlsdGVyOiB7fSxcclxuICAgICAgICBsb2FkTGlzdDogbG9hZFRyYWluaW5nTGlzdCxcclxuICAgICAgICBsb2FkRHVlczogbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMsXHJcbiAgICAgICAgdG9nZ2xlT3ZlcmR1ZTogdG9nZ2xlT3ZlcmR1ZVdyYXBwZXIsXHJcbiAgICB9KVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGFwcGx5RmlsdGVyUmVmKVxyXG4gICAgICAgICAgICBhcHBseUZpbHRlclJlZi5jdXJyZW50ID0ge1xyXG4gICAgICAgICAgICAgICAgYXBwbHk6IGhhbmRsZUZpbHRlckNoYW5nZSwgLy8gdHJpZ2dlciBpdFxyXG4gICAgICAgICAgICAgICAgb3ZlcmR1ZTogb3ZlcmR1ZUJvb2xlYW4sIC8vIHJlYWQgc25hcHNob3RcclxuICAgICAgICAgICAgICAgIHNldE92ZXJkdWU6IHRvZ2dsZU92ZXJkdWVXcmFwcGVyLCAvLyB0b2dnbGUgZGlyZWN0bHkgaWYgeW91IHdhbnRcclxuICAgICAgICAgICAgfVxyXG4gICAgfSwgW2hhbmRsZUZpbHRlckNoYW5nZSwgb3ZlcmR1ZUJvb2xlYW4sIHRvZ2dsZU92ZXJkdWVXcmFwcGVyXSlcclxuXHJcbiAgICBjb25zdCBbcmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMsIHsgbG9hZGluZzogdHJhaW5pbmdTZXNzaW9uRHVlc0xvYWRpbmcgfV0gPVxyXG4gICAgICAgIHVzZUxhenlRdWVyeShSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUywge1xyXG4gICAgICAgICAgICBmZXRjaFBvbGljeTogJ2NhY2hlLWFuZC1uZXR3b3JrJyxcclxuICAgICAgICAgICAgb25Db21wbGV0ZWQ6IChyZXNwb25zZTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UucmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMubm9kZXNcclxuICAgICAgICAgICAgICAgIGlmIChkYXRhKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gRmlsdGVyIG91dCBjcmV3IG1lbWJlcnMgd2hvIGFyZSBubyBsb25nZXIgYXNzaWduZWQgdG8gdGhlIHZlc3NlbC5cclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWx0ZXJlZERhdGEgPSBkYXRhLmZpbHRlcigoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbC5zZWFMb2dzTWVtYmVycy5ub2Rlcy5zb21lKChtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiBtLmlkID09PSBpdGVtLm1lbWJlcklEXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pLFxyXG4gICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICBjb25zdCBkdWVXaXRoU3RhdHVzID0gZmlsdGVyZWREYXRhLm1hcCgoZHVlOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHsgLi4uZHVlLCBzdGF0dXM6IEdldFRyYWluaW5nU2Vzc2lvblN0YXR1cyhkdWUpIH1cclxuICAgICAgICAgICAgICAgICAgICB9KVxyXG4gICAgICAgICAgICAgICAgICAgIC8vIFJldHVybiBvbmx5IGR1ZSB3aXRoaW4gNyBkYXlzIGFuZCBvdmVyZHVlXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gY29uc3QgZmlsdGVyZWREdWVXaXRoU3RhdHVzID0gZHVlV2l0aFN0YXR1cy5maWx0ZXIoXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgIChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICAgICAgaXRlbS5zdGF0dXMuaXNPdmVyZHVlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgLy8gICAgICAgICAgICAgKGl0ZW0uc3RhdHVzLmlzT3ZlcmR1ZSA9PT0gZmFsc2UgJiZcclxuICAgICAgICAgICAgICAgICAgICAvLyAgICAgICAgICAgICAgICAgaXRlbS5zdGF0dXMuZHVlV2l0aGluU2V2ZW5EYXlzID09PSB0cnVlKVxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICAgICAgKVxyXG4gICAgICAgICAgICAgICAgICAgIC8vICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgIC8vIClcclxuICAgICAgICAgICAgICAgICAgICAvLyBjb25zdCBncm91cGVkRHVlcyA9IGZpbHRlcmVkRHVlV2l0aFN0YXR1cy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZ3JvdXBlZER1ZXMgPSBkdWVXaXRoU3RhdHVzLnJlZHVjZShcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGFjYzogYW55LCBkdWU6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3Qga2V5ID0gYCR7ZHVlLnZlc3NlbElEfS0ke2R1ZS50cmFpbmluZ1R5cGVJRH0tJHtkdWUuZHVlRGF0ZX1gXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWFjY1trZXldKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWNjW2tleV0gPSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlkOiBkdWUuaWQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiBkdWUudmVzc2VsSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbDogZHVlLnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IGR1ZS50cmFpbmluZ1R5cGVJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBkdWUudHJhaW5pbmdUeXBlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWVEYXRlOiBkdWUuZHVlRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBkdWUuc3RhdHVzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZS50cmFpbmluZ1Nlc3Npb25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcnM6IFtdLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjY1trZXldLm1lbWJlcnMucHVzaChkdWUubWVtYmVyKVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGFjY1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7fSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcblxyXG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lcmdlZER1ZXMgPSBPYmplY3QudmFsdWVzKGdyb3VwZWREdWVzKS5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIChncm91cDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXJnZWRNZW1iZXJzID0gZ3JvdXAubWVtYmVycy5yZWR1Y2UoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKGFjYzogYW55LCBtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBleGlzdGluZ01lbWJlciA9IGFjYy5maW5kKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKG06IGFueSkgPT4gbS5pZCA9PT0gbWVtYmVyLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ01lbWJlcikge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZXhpc3RpbmdNZW1iZXIuZmlyc3ROYW1lID1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuZmlyc3ROYW1lXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ01lbWJlci5zdXJuYW1lID0gbWVtYmVyLnN1cm5hbWVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFjYy5wdXNoKG1lbWJlcilcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gYWNjXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBbXSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IGdyb3VwLmlkLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbElEOiBncm91cC52ZXNzZWxJRCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2ZXNzZWw6IGdyb3VwLnZlc3NlbCxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1R5cGVJRDogZ3JvdXAudHJhaW5pbmdUeXBlSUQsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiBncm91cC50cmFpbmluZ1R5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBncm91cC5zdGF0dXMsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZHVlRGF0ZTogZ3JvdXAuZHVlRGF0ZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTpcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZ3JvdXAudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyczogbWVyZ2VkTWVtYmVycyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgc2V0VHJhaW5pbmdTZXNzaW9uRHVlcyhtZXJnZWREdWVzKVxyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcigncmVhZFRyYWluaW5nU2Vzc2lvbkR1ZXMgZXJyb3InLCBlcnJvcilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9KVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgaWYgKGlzTG9hZGluZykge1xyXG4gICAgICAgICAgICBjb25zdCBmOiB7IG1lbWJlcnM/OiBhbnkgfSA9IHsgLi4uZmlsdGVyIH1cclxuICAgICAgICAgICAgaWYgKCttZW1iZXJJZCA+IDApIHtcclxuICAgICAgICAgICAgICAgIGYubWVtYmVycyA9IHsgaWQ6IHsgY29udGFpbnM6ICttZW1iZXJJZCB9IH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBzZXRGaWx0ZXIoZilcclxuICAgICAgICAgICAgbG9hZFRyYWluaW5nU2Vzc2lvbkR1ZXMoZilcclxuICAgICAgICAgICAgbG9hZFRyYWluaW5nTGlzdCgwLCBmKVxyXG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXHJcbiAgICAgICAgfVxyXG4gICAgfSwgW2lzTG9hZGluZ10pXHJcblxyXG4gICAgY29uc3QgW3Blcm1pc3Npb25zLCBzZXRQZXJtaXNzaW9uc10gPSB1c2VTdGF0ZTxhbnk+KGZhbHNlKVxyXG5cclxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICAgICAgc2V0UGVybWlzc2lvbnMoZ2V0UGVybWlzc2lvbnMpXHJcbiAgICB9LCBbXSlcclxuXHJcbiAgICAvLyBUcmFuc2Zvcm0gY29tcGxldGVkIHRyYWluaW5nIHNlc3Npb25zIHRvIG1hdGNoIHVuaWZpZWQgc3RydWN0dXJlXHJcbiAgICBjb25zdCB0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ3MgPSAodHJhaW5pbmdMaXN0OiBhbnlbXSkgPT4ge1xyXG4gICAgICAgIHJldHVybiB0cmFpbmluZ0xpc3QubWFwKCh0cmFpbmluZzogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIC8vIEVuc3VyZSB2ZXNzZWwgaGFzIGNvbXBsZXRlIGRhdGEgaW5jbHVkaW5nIHBvc2l0aW9uXHJcbiAgICAgICAgICAgIGNvbnN0IGNvbXBsZXRlVmVzc2VsRGF0YSA9IGdldFZlc3NlbFdpdGhJY29uKFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmcudmVzc2VsPy5pZCxcclxuICAgICAgICAgICAgICAgIHRyYWluaW5nLnZlc3NlbCxcclxuICAgICAgICAgICAgKVxyXG5cclxuICAgICAgICAgICAgcmV0dXJuIHtcclxuICAgICAgICAgICAgICAgIGlkOiB0cmFpbmluZy5pZCxcclxuICAgICAgICAgICAgICAgIGR1ZURhdGU6IHRyYWluaW5nLmRhdGUsIC8vIE1hcCBkYXRlIHRvIGR1ZURhdGUgZm9yIGNvbnNpc3RlbmN5XHJcbiAgICAgICAgICAgICAgICB2ZXNzZWxJRDogdHJhaW5pbmcudmVzc2VsPy5pZCxcclxuICAgICAgICAgICAgICAgIHZlc3NlbDogY29tcGxldGVWZXNzZWxEYXRhLFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlSUQ6IHRyYWluaW5nLnRyYWluaW5nVHlwZXM/Lm5vZGVzPy5bMF0/LmlkLFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdUeXBlOiB0cmFpbmluZy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdIHx8IHtcclxuICAgICAgICAgICAgICAgICAgICB0aXRsZTogJycsXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgbWVtYmVyczogdHJhaW5pbmcubWVtYmVycz8ubm9kZXMgfHwgW10sXHJcbiAgICAgICAgICAgICAgICB0cmFpbmVyOiB0cmFpbmluZy50cmFpbmVyLCAvLyBLZWVwIHRyYWluZXIgaW5mbyBmb3IgY29tcGxldGVkIHRyYWluaW5nc1xyXG4gICAgICAgICAgICAgICAgc3RhdHVzOiB7XHJcbiAgICAgICAgICAgICAgICAgICAgbGFiZWw6ICdDb21wbGV0ZWQnLFxyXG4gICAgICAgICAgICAgICAgICAgIGlzT3ZlcmR1ZTogZmFsc2UsXHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3M6ICdib3JkZXIgcm91bmRlZCBib3JkZXItYm9yZGVyIHRleHQtaW5wdXQgYmctb3V0ZXItc3BhY2UtNTAgcC0yIGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlcicsXHJcbiAgICAgICAgICAgICAgICAgICAgZHVlV2l0aGluU2V2ZW5EYXlzOiBmYWxzZSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICB0cmFpbmluZ0xvY2F0aW9uVHlwZTogdHJhaW5pbmcudHJhaW5pbmdMb2NhdGlvblR5cGUsXHJcbiAgICAgICAgICAgICAgICAvLyBBZGQgcHJpb3JpdHkgZm9yIHNvcnRpbmcgKGhpZ2hlciBudW1iZXIgPSBoaWdoZXIgcHJpb3JpdHkpXHJcbiAgICAgICAgICAgICAgICBwcmlvcml0eTogMCwgLy8gQ29tcGxldGVkIHRyYWluaW5ncyBoYXZlIGxvd2VzdCBwcmlvcml0eVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICAvLyBDcmVhdGUgdW5pZmllZCBhbmQgc29ydGVkIHRyYWluaW5nIGRhdGFcclxuICAgIGNvbnN0IGNyZWF0ZVVuaWZpZWRUcmFpbmluZ0RhdGEgPSAoKSA9PiB7XHJcbiAgICAgICAgLy8gVHJhbnNmb3JtIGNvbXBsZXRlZCB0cmFpbmluZ3NcclxuICAgICAgICBjb25zdCB0cmFuc2Zvcm1lZENvbXBsZXRlZCA9IHRyYW5zZm9ybUNvbXBsZXRlZFRyYWluaW5ncyhcclxuICAgICAgICAgICAgdHJhaW5pbmdMaXN0IHx8IFtdLFxyXG4gICAgICAgIClcclxuXHJcbiAgICAgICAgLy8gQWRkIHByaW9yaXR5IHRvIG92ZXJkdWUvdXBjb21pbmcgdHJhaW5pbmdzXHJcbiAgICAgICAgY29uc3QgcHJpb3JpdGl6ZWREdWVzID0gKHRyYWluaW5nU2Vzc2lvbkR1ZXMgfHwgW10pLm1hcCgoZHVlOiBhbnkpID0+ICh7XHJcbiAgICAgICAgICAgIC4uLmR1ZSxcclxuICAgICAgICAgICAgcHJpb3JpdHk6IGR1ZS5zdGF0dXM/LmlzT3ZlcmR1ZVxyXG4gICAgICAgICAgICAgICAgPyAzXHJcbiAgICAgICAgICAgICAgICA6IGR1ZS5zdGF0dXM/LmR1ZVdpdGhpblNldmVuRGF5c1xyXG4gICAgICAgICAgICAgICAgICA/IDJcclxuICAgICAgICAgICAgICAgICAgOiAxLFxyXG4gICAgICAgIH0pKVxyXG5cclxuICAgICAgICAvLyBDb21iaW5lIGFsbCB0cmFpbmluZyBkYXRhXHJcbiAgICAgICAgY29uc3QgYWxsVHJhaW5pbmdzID0gWy4uLnByaW9yaXRpemVkRHVlcywgLi4udHJhbnNmb3JtZWRDb21wbGV0ZWRdXHJcblxyXG4gICAgICAgIC8vIFNvcnQgYnkgcHJpb3JpdHkgKGRlc2NlbmRpbmcpLCB0aGVuIGJ5IGR1ZSBkYXRlIChhc2NlbmRpbmcgZm9yIG92ZXJkdWUvdXBjb21pbmcsIGRlc2NlbmRpbmcgZm9yIGNvbXBsZXRlZClcclxuICAgICAgICByZXR1cm4gYWxsVHJhaW5pbmdzLnNvcnQoKGE6IGFueSwgYjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIC8vIEZpcnN0IHNvcnQgYnkgcHJpb3JpdHkgKG92ZXJkdWUgPiB1cGNvbWluZyA+IG90aGVyID4gY29tcGxldGVkKVxyXG4gICAgICAgICAgICBpZiAoYS5wcmlvcml0eSAhPT0gYi5wcmlvcml0eSkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGIucHJpb3JpdHkgLSBhLnByaW9yaXR5XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC8vIFdpdGhpbiBzYW1lIHByaW9yaXR5LCBzb3J0IGJ5IGRhdGVcclxuICAgICAgICAgICAgY29uc3QgZGF0ZUEgPSBuZXcgRGF0ZShhLmR1ZURhdGUgfHwgMCkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgIGNvbnN0IGRhdGVCID0gbmV3IERhdGUoYi5kdWVEYXRlIHx8IDApLmdldFRpbWUoKVxyXG5cclxuICAgICAgICAgICAgLy8gRm9yIG92ZXJkdWUgYW5kIHVwY29taW5nIChwcmlvcml0eSA+IDApLCBzb3J0IGJ5IGR1ZSBkYXRlIGFzY2VuZGluZyAoZWFybGllc3QgZmlyc3QpXHJcbiAgICAgICAgICAgIGlmIChhLnByaW9yaXR5ID4gMCkge1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGRhdGVBIC0gZGF0ZUJcclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLy8gRm9yIGNvbXBsZXRlZCB0cmFpbmluZ3MgKHByaW9yaXR5ID0gMCksIHNvcnQgYnkgZGF0ZSBkZXNjZW5kaW5nIChtb3N0IHJlY2VudCBmaXJzdClcclxuICAgICAgICAgICAgcmV0dXJuIGRhdGVCIC0gZGF0ZUFcclxuICAgICAgICB9KVxyXG4gICAgfVxyXG5cclxuICAgIC8vIENvbWJpbmVkIGxvYWRpbmcgc3RhdGUgZm9yIHVuaWZpZWQgdmlld1xyXG4gICAgY29uc3QgaXNVbmlmaWVkRGF0YUxvYWRpbmcgPSBleGNsdWRlRmlsdGVycy5pbmNsdWRlcygnb3ZlcmR1ZVRvZ2dsZScpXHJcbiAgICAgICAgPyB0cmFpbmluZ0xpc3RMb2FkaW5nIHx8IHRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nXHJcbiAgICAgICAgOiBmYWxzZVxyXG5cclxuICAgIC8vIENyZWF0ZSB1bmlmaWVkIGRhdGFzZXQgd2hlbiBvdmVyZHVlVG9nZ2xlIGlzIGV4Y2x1ZGVkXHJcbiAgICBjb25zdCBnZXRVbmlmaWVkVHJhaW5pbmdEYXRhID0gKCkgPT4ge1xyXG4gICAgICAgIGlmIChleGNsdWRlRmlsdGVycy5pbmNsdWRlcygnb3ZlcmR1ZVRvZ2dsZScpKSB7XHJcbiAgICAgICAgICAgIGNvbnN0IHRyYW5zZm9ybWVkQ29tcGxldGVkVHJhaW5pbmcgPVxyXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtVHJhaW5pbmdMaXN0VG9PdmVyZHVlRm9ybWF0KHRyYWluaW5nTGlzdCB8fCBbXSlcclxuICAgICAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgICAgIC4uLih0cmFpbmluZ1Nlc3Npb25EdWVzIHx8IFtdKSxcclxuICAgICAgICAgICAgICAgIC4uLnRyYW5zZm9ybWVkQ29tcGxldGVkVHJhaW5pbmcsXHJcbiAgICAgICAgICAgIF1cclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIHRyYWluaW5nU2Vzc2lvbkR1ZXMgfHwgW11cclxuICAgIH1cclxuXHJcbiAgICBpZiAoXHJcbiAgICAgICAgIXBlcm1pc3Npb25zIHx8XHJcbiAgICAgICAgKCFoYXNQZXJtaXNzaW9uKCdFRElUX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXHJcbiAgICAgICAgICAgICFoYXNQZXJtaXNzaW9uKCdWSUVXX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpICYmXHJcbiAgICAgICAgICAgICFoYXNQZXJtaXNzaW9uKCdSRUNPUkRfVFJBSU5JTkcnLCBwZXJtaXNzaW9ucykgJiZcclxuICAgICAgICAgICAgIWhhc1Blcm1pc3Npb24oJ1ZJRVdfTUVNQkVSX1RSQUlOSU5HJywgcGVybWlzc2lvbnMpKVxyXG4gICAgKSB7XHJcbiAgICAgICAgcmV0dXJuICFwZXJtaXNzaW9ucyA/IChcclxuICAgICAgICAgICAgPExvYWRpbmcgLz5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgICA8TG9hZGluZyBlcnJvck1lc3NhZ2U9XCJPb3BzIFlvdSBkbyBub3QgaGF2ZSB0aGUgcGVybWlzc2lvbiB0byB2aWV3IHRoaXMgc2VjdGlvbi5cIiAvPlxyXG4gICAgICAgIClcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBjb2x1bW5zID0gY3JlYXRlQ29sdW1ucyhbXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RpdGxlJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJDb21wbGV0ZWRcIiAvPlxyXG4gICAgICAgICAgICApLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZzogYW55ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxNb2JpbGVUcmFpbmluZ0NhcmRcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGF0YT17dHJhaW5pbmd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZT1cImNvbXBsZXRlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGVBID0gbmV3IERhdGUocm93QT8ub3JpZ2luYWw/LmRhdGUgfHwgMCkuZ2V0VGltZSgpXHJcbiAgICAgICAgICAgICAgICBjb25zdCBkYXRlQiA9IG5ldyBEYXRlKHJvd0I/Lm9yaWdpbmFsPy5kYXRlIHx8IDApLmdldFRpbWUoKVxyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiBkYXRlQiAtIGRhdGVBXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndHJhaW5pbmdEcmlsbHNDb21wbGV0ZWQnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnbGVmdCcsXHJcbiAgICAgICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXJcclxuICAgICAgICAgICAgICAgICAgICBjb2x1bW49e2NvbHVtbn1cclxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlRyYWluaW5nL2RyaWxscyBjb21wbGV0ZWRcIlxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ3RhYmxldC1tZCcsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxQPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcudHJhaW5pbmdUeXBlcy5ub2Rlc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB0cmFpbmluZy50cmFpbmluZ1R5cGVzLm5vZGVzLm1hcCgoaXRlbTogYW55KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBrZXk9e2l0ZW0uaWR9PntpdGVtLnRpdGxlfSwmbmJzcDs8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvUD5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc29ydGluZ0ZuOiAocm93QTogYW55LCByb3dCOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQSA9XHJcbiAgICAgICAgICAgICAgICAgICAgcm93QT8ub3JpZ2luYWw/LnRyYWluaW5nVHlwZXM/Lm5vZGVzPy5bMF0/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPVxyXG4gICAgICAgICAgICAgICAgICAgIHJvd0I/Lm9yaWdpbmFsPy50cmFpbmluZ1R5cGVzPy5ub2Rlcz8uWzBdPy50aXRsZSB8fCAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd3aGVyZScsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJXaGVyZVwiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYW5kc2NhcGUnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZyA9IHJvdy5vcmlnaW5hbFxyXG5cclxuICAgICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbm93cmFwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcudmVzc2VsPy50aXRsZSB8fFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnRyYWluaW5nTG9jYXRpb25UeXBlIHx8XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExvY2F0aW9uTW9kYWxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZlc3NlbD17dHJhaW5pbmcudmVzc2VsfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWNvbkNsYXNzTmFtZT1cInNpemUtOFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcblxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LnZlc3NlbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3RyYWluZXInLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAnY2VudGVyJyxcclxuICAgICAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJUcmFpbmVyXCIgLz5cclxuICAgICAgICAgICAgKSxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IHRyYWluaW5nID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ub3dyYXBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgeyFpc1Zlc3NlbFZpZXcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXIgc2l6ZT1cInNtXCIgdmFyaWFudD17J3NlY29uZGFyeSd9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy50cmFpbmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmcudHJhaW5lci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nLnRyYWluZXIuZmlyc3ROYW1lfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFpbmluZy50cmFpbmVyLnN1cm5hbWUgPz8gJyd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhciBzaXplPVwic21cIiB2YXJpYW50PXsnc2Vjb25kYXJ5J30+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDcmV3SW5pdGlhbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRyYWluaW5nLnRyYWluZXIuZmlyc3ROYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZy50cmFpbmVyLnN1cm5hbWUsXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVG9vbHRpcFRyaWdnZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcudHJhaW5lci5maXJzdE5hbWV9eycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYWluaW5nLnRyYWluZXIuc3VybmFtZSA/PyAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dBPy5vcmlnaW5hbD8udHJhaW5lciAmJiByb3dBPy5vcmlnaW5hbD8udHJhaW5lcj8uZmlyc3ROYW1lfSAke3Jvd0E/Lm9yaWdpbmFsPy50cmFpbmVyICYmIHJvd0E/Lm9yaWdpbmFsPy50cmFpbmVyPy5zdXJuYW1lfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dCPy5vcmlnaW5hbD8udHJhaW5lciAmJiByb3dCPy5vcmlnaW5hbD8udHJhaW5lcj8uZmlyc3ROYW1lfSAke3Jvd0I/Lm9yaWdpbmFsPy50cmFpbmVyICYmIHJvd0I/Lm9yaWdpbmFsPy50cmFpbmVyPy5zdXJuYW1lfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICd3aG8nLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIldob1wiIC8+XHJcbiAgICAgICAgICAgICksXHJcbiAgICAgICAgICAgIGJyZWFrcG9pbnQ6ICdsYXB0b3AnLFxyXG4gICAgICAgICAgICBjZWxsOiAoeyByb3cgfTogeyByb3c6IGFueSB9KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB0cmFpbmluZyA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBmbGV4IGl0ZW1zLWVuZCBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmcubWVtYmVycy5ub2Rlcy5tYXAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAobWVtYmVyOiBhbnksIGluZGV4OiBudW1iZXIpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBrZXk9e2luZGV4fT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9eydzZWNvbmRhcnknfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cInRleHQtc21cIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRDcmV3SW5pdGlhbHMoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtZW1iZXIuc3VybmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXBUcmlnZ2VyPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIuZmlyc3ROYW1lfXsnICd9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlci5zdXJuYW1lID8/ICcnfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgIHNvcnRpbmdGbjogKHJvd0E6IGFueSwgcm93QjogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUEgPVxyXG4gICAgICAgICAgICAgICAgICAgIGAke3Jvd0E/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5maXJzdE5hbWUgPz8gJyd9ICR7cm93QT8ub3JpZ2luYWw/Lm1lbWJlcnM/Lm5vZGVzPy5bMF0/LnN1cm5hbWUgPz8gJyd9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICBjb25zdCB2YWx1ZUIgPVxyXG4gICAgICAgICAgICAgICAgICAgIGAke3Jvd0I/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5maXJzdE5hbWUgPz8gJyd9ICR7cm93Qj8ub3JpZ2luYWw/Lm1lbWJlcnM/Lm5vZGVzPy5bMF0/LnN1cm5hbWUgPz8gJyd9YCB8fFxyXG4gICAgICAgICAgICAgICAgICAgICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICBdKVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGxcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi01XCI+XHJcbiAgICAgICAgICAgICAgICA8Q2FyZD5cclxuICAgICAgICAgICAgICAgICAgICA8VHJhaW5pbmdMaXN0RmlsdGVyXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlcklkPXttZW1iZXJJZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbHRlckNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb3ZlcmR1ZVN3aXRjaGVyPXshb3ZlcmR1ZUJvb2xlYW59XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4Y2x1ZGVGaWx0ZXJzPXtleGNsdWRlRmlsdGVyc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAge2V4Y2x1ZGVGaWx0ZXJzLmluY2x1ZGVzKCdvdmVyZHVlVG9nZ2xlJykgPyAoXHJcbiAgICAgICAgICAgICAgICBpc1VuaWZpZWREYXRhTG9hZGluZyA/IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtOCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIExvYWRpbmcgdHJhaW5pbmcgZGF0YS4uLlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8T3ZlcmR1ZVRyYWluaW5nTGlzdFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFpbmluZ1Nlc3Npb25EdWVzPXtnZXRVbmlmaWVkVHJhaW5pbmdEYXRhKCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGhpZGVDcmV3Q29sdW1uPXt0cnVlfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17MjB9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgKSA6IG92ZXJkdWVCb29sZWFuID8gKFxyXG4gICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlc0xvYWRpbmcgPyAoXHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTggdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxMb2FkZXIyIGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNCBhbmltYXRlLXNwaW5cIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBMb2FkaW5nIG92ZXJkdWUgdHJhaW5pbmcuLi5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICAgICAgPE92ZXJkdWVUcmFpbmluZ0xpc3RcclxuICAgICAgICAgICAgICAgICAgICAgICAgdHJhaW5pbmdTZXNzaW9uRHVlcz17dHJhaW5pbmdTZXNzaW9uRHVlc31cclxuICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgPD5cclxuICAgICAgICAgICAgICAgICAgICB7dHJhaW5pbmdMaXN0Py5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8RGF0YVRhYmxlXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2x1bW5zPXtjb2x1bW5zfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YT17dHJhaW5pbmdMaXN0fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcGFnZVNpemU9ezIwfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbHRlckNoYW5nZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNob3dUb29sYmFyPXtmYWxzZX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGdyb3VwIGJvcmRlci1iIGhvdmVyOiBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGNvbC1zcGFuLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBnYXAtMiBwLTIgcHQtNFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCIhdy1bNzVweF0gaC1hdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAxNDcgMTQ3LjAxXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzIuNDUsMGMxNy4yNi0uMDcsMzIuNjgsNS4xMiw0Ni4yOSwxNS41NiwxMC42LDguMzksMTguMzgsMTguODgsMjMuMzUsMzEuNDcsNS4wOCwxMy40NSw2LjIxLDI3LjIzLDMuNDEsNDEuMzQtMy4yMywxNS4wOC0xMC4zOCwyNy45Mi0yMS40NCwzOC41Mi0xMi4yMiwxMS40Mi0yNi42OSwxOC4wMS00My40NCwxOS43OC0xNS42NiwxLjQyLTMwLjMxLTEuNzUtNDMuOTUtOS41Mi0xMy4xMS03LjczLTIyLjk4LTE4LjQ0LTI5LjYxLTMyLjEzQy45LDkxLjgyLTEuMjIsNzcuOTguNjcsNjMuNTFjMi4zNi0xNi4xMiw5LjE3LTI5Ljk4LDIwLjQ0LTQxLjU4QzMzLjI1LDkuNzgsNDcuOTEsMi42Myw2NS4wOC40OWMyLjQ2LS4yNyw0LjkxLS40Myw3LjM3LS40OVpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjZmZmZmZmXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTcyLjQ1LDBjMTcuMjYtLjA3LDMyLjY4LDUuMTIsNDYuMjksMTUuNTYsMTAuNiw4LjM5LDE4LjM4LDE4Ljg4LDIzLjM1LDMxLjQ3LDUuMDgsMTMuNDUsNi4yMSwyNy4yMywzLjQxLDQxLjM0LTMuMjMsMTUuMDgtMTAuMzgsMjcuOTItMjEuNDQsMzguNTItMTIuMjIsMTEuNDItMjYuNjksMTguMDEtNDMuNDQsMTkuNzgtMTUuNjYsMS40Mi0zMC4zMS0xLjc1LTQzLjk1LTkuNTItMTMuMTEtNy43My0yMi45OC0xOC40NC0yOS42MS0zMi4xM0MuOSw5MS44Mi0xLjIyLDc3Ljk4LjY3LDYzLjUxYzIuMzYtMTYuMTIsOS4xNy0yOS45OCwyMC40NC00MS41OEMzMy4yNSw5Ljc4LDQ3LjkxLDIuNjMsNjUuMDguNDljMi40Ni0uMjcsNC45MS0uNDMsNy4zNy0uNDlaTTgyLjQ5LDE5LjQ2Yy0yLjAxLTEuMS00LjE0LTEuODUtNi4zOS0yLjI2LTEuNDItLjE1LTIuODQtLjM1LTQuMjUtLjYxLTEuNDYtLjI2LTIuNzktLjgxLTQuMDEtMS42M2wtLjM1LS4zNWMtLjI5LS41My0uNi0xLjA0LS45My0xLjU0LS4wOS43LS4xNiwxLjQxLS4yMSwyLjEyLjAzLjQuMDguOC4xNiwxLjE5LjEzLjQ0LjI3Ljg4LjQ0LDEuMzEtLjUtLjYxLS44Ni0xLjI5LTEuMS0yLjA1LS4wOC0uNC0uMTctLjc4LS4yOC0xLjE3LTEuNzIuOTItMi43MywyLjM2LTMuMDMsNC4yOS0uMTUsMS4zLS4wNywyLjU5LjI2LDMuODUtLjAxLDAtLjAzLjAxLS4wNS4wMi0xLjItLjU4LTIuMjUtMS4zOC0zLjE1LTIuMzgtLjM1LS40MS0uNy0uODMtMS4wMy0xLjI2LTMuNjUsNC43MS00LjU4LDkuOTItMi44LDE1LjYzLjIyLjY3LjQ4LDEuMzIuNzcsMS45Ni0uODguOS0xLjMyLDEuOTktMS4zMSwzLjI3LjA3LDIuNDYuMDYsNC45MS0uMDUsNy4zNywwLC43My4xNSwxLjQxLjQ5LDIuMDUuNS42NiwxLjE0Ljg0LDEuOTEuNTEuMDQsMS4wOC4xNCwyLjE1LjI4LDMuMjIuMzIsMS42LjkxLDMuMDksMS43Nyw0LjQ4LDEuMDIsMS42OSwyLjMsMy4xNywzLjgzLDQuNDMuMDMsMi41NS0uMjEsNS4wNy0uNzUsNy41Ni0uMjUsMS4wOC0uNiwyLjEyLTEuMDcsMy4xMy0uMDYtLjgyLS4wOC0xLjY1LS4wNy0yLjQ3LTMuNTEsMS4wNi03LjAzLDIuMTMtMTAuNTUsMy4yLS4wNS4xOC0uMDUuMzUsMCwuNTQtMywxLjAzLTUuNzUsMi41LTguMjYsNC40MS0yLjQ5LDEuOTUtNC4yOSw0LjQxLTUuMzksNy40LTEuNDQsMy43LTIuNDgsNy41MS0zLjEzLDExLjQzLS44NSw1LjEzLTEuMzksMTAuMjktMS41OSwxNS40OS0uMjgsNi44OC0uMjcsMTMuNzUuMDUsMjAuNjItMTEuODUtOC4xOS0yMC41Ni0xOC45NC0yNi4xMy0zMi4yNEMxLjA2LDg3LjE5LS4yMiw3My4wMywyLjc3LDU4LjQ3YzMuNDEtMTUuMywxMC44Ni0yOC4yMSwyMi4zNy0zOC43MUMzNy41Myw4Ljc3LDUyLjA1LDIuNjQsNjguNjgsMS4zOGMxNi4zMS0uOTYsMzEuMjcsMy4wMyw0NC44OSwxMS45NSwxMi43Nyw4LjY1LDIxLjk1LDIwLjE3LDI3LjU1LDM0LjU1LDUuMSwxMy43NSw2LjAzLDI3Ljc4LDIuOCw0Mi4wOS0zLjY2LDE1LjA4LTExLjI1LDI3LjczLTIyLjc5LDM3Ljk2LTIuMTcsMS44OC00LjQzLDMuNjMtNi43OSw1LjI1LjItNS4yNS4yNi0xMC41MS4xOS0xNS43Ny0uMDgtNi4zLS41OC0xMi41Ny0xLjQ5LTE4LjgtLjYxLTQuMTctMS42NC04LjIzLTMuMDgtMTIuMTgtLjYzLTEuNy0xLjQzLTMuMy0yLjQzLTQuODEtMS43Mi0yLjItMy44LTMuOTgtNi4yMy01LjM0LTEuNy0uOTctMy40Ny0xLjc4LTUuMzItMi40MywwLS4xNywwLS4zNC0uMDUtLjUxLTMuNTEtMS4wNy03LjAzLTIuMTQtMTAuNTUtMy4yLDAsLjY3LDAsMS4zNC0uMDIsMi4wMS0uNzEtMS42MS0xLjE4LTMuMjktMS40LTUuMDQtLjI4LTEuOTItLjQtMy44NS0uMzctNS43OSwzLjUxLTMuMDUsNS4zOC02LjksNS42LTExLjU3LDEuMDkuNDMsMS44NS4xMSwyLjI5LS45OC4xNC0uMzYuMjMtLjc0LjI4LTEuMTIuMTYtMi43MS4zOS01LjQyLjY4LTguMTIuMDItMS4xNi0uMzUtMi4xNi0xLjEyLTMuMDEuNzItMiwuOTgtNC4wNi43Ny02LjE4LS4yMy0zLjAyLS45OS01LjktMi4yOS04LjYzLS4yNS0uNDktLjYtLjg5LTEuMDUtMS4xOS0uOS0uNTctMS44NS0xLjA1LTIuODUtMS40NS0yLjMyLS45My00LjY2LTEuNjktNy0yLjI5bDIuOTQsMi4xYy4yMy4xOS40NC4zOC42NS41OFpNNjcuNzksMTYuNDNjMS41Ny44MiwzLjIzLDEuMzMsNC45OSwxLjU2LDMuNjQuMTcsNywxLjIxLDEwLjA4LDMuMTMuNDYuMzIuOTEuNjQsMS4zNS45OC41MS41LDEuMDQuOTgsMS41OSwxLjQyLS4xNi0uNzktLjM3LTEuNTgtLjYzLTIuMzgtLjItLjQ1LS40NC0uODgtLjcyLTEuMjgsMS4xNy4zNywyLjI5Ljg3LDMuMzYsMS40OS41MS4zLjg4LjczLDEuMSwxLjI4LDEuNDksMy4zNSwyLjE0LDYuODUsMS45NiwxMC41LS4xLDEuNTYtLjU4LDMtMS40NSw0LjI5LjE4LTMuMTMtLjk5LTUuNTktMy41Mi03LjQtLjA4LS4wMy0uMTUtLjAzLS4yMywwLTQuMDcsMS4yNC04LjIzLDIuMS0xMi40NiwyLjU3LTIuMTMuMjMtNC4yNi4yMS02LjM5LS4wNS0xLjM2LS4xNy0yLjYtLjY0LTMuNzMtMS40LS4yMS0uMTYtLjQtLjM0LS41OC0uNTQtLjE5LS4yNi0uMzgtLjUtLjU4LS43NS0xLjY0Ljk1LTIuNzksMi4zMi0zLjQzLDQuMTEtLjMuODUtLjUsMS43Mi0uNjEsMi42MS0xLjQxLTIuODYtMS45Ny01Ljg4LTEuNjgtOS4wNS4yOS0yLjM4LDEuMTEtNC41NiwyLjQ1LTYuNTMsMS4wMSwxLjEzLDIuMiwyLjA0LDMuNTUsMi43My43OC4zMSwxLjU5LjUsMi40My41OC0uNDEtLjk4LS43LTEuOTktLjg2LTMuMDMtLjItMS4xOC0uMTEtMi4zMy4yOC0zLjQ1LjIxLS40OS40OS0uOTIuODQtMS4zMS43LDEuODMsMS45NSwzLjEzLDMuNzYsMy45LjgzLjI4LDEuNjcuNTEsMi41Mi43LS41LS41NC0xLjAxLTEuMDctMS41Mi0xLjYxLS44Mi0uOS0xLjQzLTEuOTMtMS44NC0zLjA4Wk01OS4wNiwzNy4zOGMuMDItMS44OS42MS0zLjU5LDEuNzUtNS4wOS4yNy0uMjcuNTQtLjU0LjgyLS43OS45NS45MSwyLjA3LDEuNTQsMy4zNiwxLjg5LDEuNjIuNDIsMy4yNy42MSw0Ljk1LjU4LDIuNTctLjA1LDUuMTItLjMsNy42NS0uNzcsMi42OS0uNDgsNS4zNC0xLjExLDcuOTYtMS44OSwxLjk5LDEuNTcsMi44NiwzLjYyLDIuNjQsNi4xNi0xLjc3LTEuNzUtMy45LTIuNTEtNi4zOS0yLjI2LS42NC4wNC0xLjI4LjEyLTEuOTEuMjMtNC4yMS4wMy04LjQzLjAzLTEyLjY1LDAtMS4zNi0uMjYtMi43My0uMzItNC4xMS0uMTktMS41Ny4zMi0yLjkyLDEuMDItNC4wNiwyLjEyWk03MC42MywzNi42OGMxLjk0LS4wNiwzLjg4LS4wNiw1LjgzLS4wMi0uNjUuNDEtMS4xNC45Ni0xLjQ3LDEuNjYtLjMyLS41NS0uOC0uODYtMS40Mi0uOTMtLjI3LDAtLjUyLjA3LS43NS4yMS0uMjguMjEtLjUxLjQ1LS43LjcyLS4zNC0uNy0uODQtMS4yNC0xLjQ5LTEuNjNaTTkwLjY1LDM3Ljc1cy4wOCwwLC4xMi4wNWMuNC43MS41NCwxLjQ3LjQyLDIuMjktLjI4LDIuNDgtLjUsNC45Ny0uNjUsNy40Ny0uMDQuMzktLjE3Ljc1LS4zNywxLjA3LS4wNS4wNi0uMTIuMS0uMTkuMTQtLjI4LS4xMi0uNTQtLjI4LS43NS0uNTEtLjAzLS45Mi0uMDMtMS44MywwLTIuNzUuNzctMS42My45NS0zLjMzLjU2LTUuMDktLjEtLjM4LS4yMy0uNzYtLjQtMS4xMi40OC0uNDcuOS0uOTgsMS4yNi0xLjU0Wk01Ny4wNiwzNy44Yy4wNy4wMi4xMy4wNy4xNi4xNC4xNC4yOC4yOS41NC40Ny43OS4wMy4yMy4wMy40NywwLC43LS42NCwxLjY3LS43LDMuMzctLjE5LDUuMDksMCwxLjI0LjAzLDIuNDcuMDcsMy43MS0uMDEuMDctLjAzLjE0LS4wNS4yMS0uMTguMTQtLjM4LjI1LS42MS4zMy0uMTYtLjA2LS4yNi0uMTYtLjMtLjMzLS4xNC0uMzktLjIxLS44LS4yMS0xLjIxLjEtMi40LjEyLTQuODEuMDUtNy4yMS0uMDMtLjgxLjE4LTEuNTQuNjEtMi4yMlpNNzMuNDgsMzguNTljLjE0LDAsLjI2LjA3LjM1LjE5LjM3LjUyLjYzLDEuMS43OSwxLjczLjM1LDIuODcsMS42MSw1LjI2LDMuNzYsNy4xNiwyLjg0LDIuMjEsNS43NywyLjMyLDguNzcuMzMuMjgtLjIyLjU2LS40Ny44Mi0uNzIuNDEsNi41MS0yLjEzLDExLjQ4LTcuNjMsMTQuOTEtMy4yNCwxLjY4LTYuNjYsMi4yMS0xMC4yNywxLjYxLTIuMzctLjQ3LTQuNDMtMS41LTYuMjEtMy4xLTEuODctMS42OC0zLjI5LTMuNjktNC4yNy02LS40OC0xLjI5LS43My0yLjYzLS43NS00LjAxLS4wOC0xLjI5LS4xMS0yLjU4LS4wOS0zLjg3LDEuNjgsMS45NCwzLjgsMi43OCw2LjM3LDIuNTQsMS44LS4zNSwzLjMxLTEuMiw0LjU1LTIuNTQsMS41NS0xLjcxLDIuNDgtMy43MiwyLjgtNi4wMi4xNi0uODIuNDktMS41NSwxLTIuMTlaTTY0LjEsNTEuNDdoMTguNzZjLS4zMSwzLjEtMS43NSw1LjUxLTQuMzQsNy4yMS0zLjMzLDEuOTMtNi42OCwxLjk1LTEwLjAzLjA1LTIuNjQtMS43LTQuMS00LjEyLTQuMzktNy4yNlpNODIuMyw2Mi4yOXMuMDYuMDUuMDcuMDljLjAyLDIuOC4zOSw1LjU2LDEuMTIsOC4yNi4zNywxLjI4LjkyLDIuNDYsMS42NiwzLjU1LS4zOCwzLjAzLTEuMzQsNS44Ni0yLjg3LDguNDktMS45NywzLjE1LTQuNzksNS4wNC04LjQ3LDUuNjctMi41Ni0uMTktNC44LTEuMTItNi43Mi0yLjgtMS44NC0xLjc2LTMuMTktMy44NS00LjA0LTYuMjgtLjU2LTEuNTYtLjk1LTMuMTctMS4xNy00LjgxLjQ5LS42Ljg4LTEuMjcsMS4xNy0yLjAxLjc0LTEuOTQsMS4yLTMuOTUsMS40LTYuMDIuMTMtMS4xNi4yLTIuMzMuMjMtMy41LjAzLS4wNC4wNy0uMDUuMTItLjAyLDEuOTUsMS4zLDQuMDksMi4wNSw2LjQ0LDIuMjQsMy4zMS4yOSw2LjQ1LS4zLDkuNDMtMS43Ny41OC0uMzIsMS4xMi0uNjksMS42My0xLjFaTTk1LjgzLDc1LjA4YzIuODksMS4wMyw1LjUzLDIuNDksNy45Myw0LjM2LDEuNzMsMS4zOSwzLjA3LDMuMDcsNC4wNCw1LjA2LDEuNDcsMy4yNSwyLjU2LDYuNjIsMy4yNywxMC4xMy45OCw0Ljg3LDEuNjIsOS43OCwxLjkxLDE0Ljc0LjUxLDguMjMuNTMsMTYuNDYuMDUsMjQuNjgtMTMuNzIsOC44MS0yOC43MywxMi42Ni00NS4wNSwxMS41NS0xMi4zMy0uOTktMjMuNjYtNC44NC0zMy45OS0xMS41NS0uNDMtOC4zMS0uNC0xNi42Mi4wOS0yNC45Mi4zLTQuOTguOTUtOS45MSwxLjk2LTE0Ljc5LjY2LTMuMiwxLjY0LTYuMjksMi45NC05LjI5Ljg3LTIuMDMsMi4xNC0zLjc2LDMuOC01LjIsMi40OC0yLjA4LDUuMjctMy42Niw4LjM1LTQuNzQuNiw2Ljc1LjIxLDEzLjQzLTEuMTQsMjAuMDYtLjQxLDIuMTQtLjk1LDQuMjQtMS42Myw2LjMtLjM4LDEuMDgtLjg5LDIuMS0xLjU0LDMuMDMtLjI4LjMzLS42LjYtLjk2LjgyLS4xNi4wOC0uMzQuMTMtLjUxLjE2djE2LjhoNTYuMjd2LTE2LjhjLS41OC0uMTUtMS4wNS0uNDYtMS40Mi0uOTMtLjctLjk5LTEuMjUtMi4wNi0xLjYzLTMuMjItLjc0LTIuMjYtMS4zMS00LjU2LTEuNzMtNi45MS0xLTQuOTktMS40MS0xMC4wMy0xLjIxLTE1LjEyLjA0LTEuNDIuMTEtMi44My4yMS00LjI1WlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMwNTIzNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGxSdWxlPVwiZXZlbm9kZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eT1cIi45N1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwcHhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk02My43OCwzNS43NGMxLjE0LDAsMi4yOC4xLDMuNDEuMjh2LjYxYzEuNzYtLjM3LDMuMTcuMTUsNC4yMiwxLjU5LjE2LjI3LjI4LjU2LjM1Ljg2LS4xNy40OS0uMzMuOTgtLjQ3LDEuNDcuMTguMDguMzYuMTMuNTYuMTQtLjM4LDIuOTktMS44LDUuMzQtNC4yNSw3LjA3LTIuNjgsMS41Ni01LjIzLDEuMzctNy42NS0uNTYtMS42NC0xLjUzLTIuMzctMy40Mi0yLjE3LTUuNjcuMTQtMS41OS44MS0yLjkyLDEuOTgtMy45OSwxLjE2LTEsMi41LTEuNiw0LjAxLTEuOFpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMjk5OGU5XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTgyLjA3LDM1Ljc0YzIuNDEtLjEzLDQuNDEuNzEsNiwyLjUyLDEuMjcsMS43MSwxLjY1LDMuNjEsMS4xMiw1LjY5LS43MSwyLjM5LTIuMjUsMy45My00LjY0LDQuNjQtMS4zNS4zNS0yLjY4LjI2LTMuOTctLjI4LTEuODMtLjg5LTMuMjMtMi4yMy00LjE4LTQuMDQtLjY1LTEuMTktMS4wMy0yLjQ3LTEuMTQtMy44My4xOS0uMDIuMzctLjA2LjU2LS4wOS0uMTEtLjQ1LS4yNS0uOS0uNDItMS4zMy4yMy0uODMuNzItMS40NywxLjQ1LTEuOTEuMy0uMTguNjEtLjM0LjkzLS40Ny43MS0uMDIsMS40My0uMDMsMi4xNS0uMDJ2LS42MWMuNzItLjExLDEuNDQtLjIsMi4xNS0uMjhaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzI5OThlOVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwcHhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk02NS41NSw0MC42Yy45NywwLDEuNDUuNDgsMS40MiwxLjQ1LS4yMy43NS0uNzMsMS4wNy0xLjUyLjk2LS42Ni0uMjctLjk1LS43Ni0uODYtMS40Ny4xNi0uNDguNDgtLjc5Ljk2LS45M1pcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDI0NDUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTgxLjE4LDQwLjZjLjctLjA0LDEuMTguMjgsMS40Mi45My4wNiwxLjA4LS40NSwxLjU3LTEuNTIsMS40Ny0uODEtLjM3LTEuMDUtLjk3LS43Mi0xLjguMjEtLjMuNDgtLjUuODItLjYxWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMwNTI0NTFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNjIuODQsNTAuMjVoMjEuMjNjLjEsMy43OC0xLjM1LDYuOC00LjM0LDkuMDgtMywyLjAzLTYuMjMsMi41MS05LjcxLDEuNDUtMy42NS0xLjM1LTUuOTYtMy45MS02LjkzLTcuNjgtLjE4LS45NC0uMjctMS44OS0uMjYtMi44NVpNNjQuMSw1MS40N2MuMjksMy4xNCwxLjc1LDUuNTYsNC4zOSw3LjI2LDMuMzUsMS45LDYuNywxLjg5LDEwLjAzLS4wNSwyLjU5LTEuNyw0LjAzLTQuMTEsNC4zNC03LjIxaC0xOC43NlpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDUyMjUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTczLjIsODkuNTRjLjE5LjA2LjM3LjA2LjU2LDAsNC4zNi0uNjcsNy42My0yLjkxLDkuODItNi43MiwxLjQ5LTIuNzgsMi40My01LjczLDIuOC04Ljg3bC4yMS0yLjI0YzIuNy44NSw1LjQsMS42OCw4LjEyLDIuNDctLjI5LDMuODEtLjM2LDcuNjItLjIxLDExLjQzLjMzLDQuNDQsMS4wMiw4LjgzLDIuMDUsMTMuMTYuNDYsMS45MSwxLjEyLDMuNzUsMi4wMSw1LjUxLjMuNTQuNjcsMS4wMywxLjEsMS40Ny4yMi4yMS40OC4zOS43NS41NHYxNC43OWgtNTMuODV2LTE0Ljc5Yy41NC0uMy45OC0uNywxLjMzLTEuMjEuNTYtLjg1LDEuMDMtMS43NSwxLjQtMi43MS45Ny0yLjc1LDEuNjgtNS41NywyLjE1LTguNDUuOTUtNS4xMiwxLjMxLTEwLjI4LDEuMDctMTUuNDktLjA0LTEuMzYtLjEzLTIuNzMtLjI2LTQuMDguMDEtLjA2LjAzLS4xMS4wNS0uMTYsMi42OS0uODMsNS4zOC0xLjY2LDguMDctMi40Ny4xNiwzLjM2LjkxLDYuNTgsMi4yNiw5LjY2LDEuMjUsMi43NywzLjE1LDQuOTYsNS43Miw2LjU2LDEuNTEuODYsMy4xMywxLjQsNC44NSwxLjYxWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMyOTk4ZTlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNDUuMzQsMTI1LjhoMjMuODR2Ni42M2gtMjMuODR2LTYuNjNaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzA1MjM1MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzAuMTcsMTI1LjhoNi41OHY2LjYzaC02LjU4di02LjYzWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMwNTIyNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTc3Ljc3LDEyNS44aDIzLjg0djYuNjNoLTIzLjg0di02LjYzWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMwNTIzNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTY3Ljk4LDEyNy4wMXY0LjJoLTIxLjQydi00LjJoMjEuNDJaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzJhOTllYVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzUuNTgsMTI3LjAxdjQuMmgtNC4ydi00LjJoNC4yWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMyYTk5ZWFcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTc4Ljk5LDEyNy4wMWgyMS40MnY0LjJoLTIxLjQydi00LjJaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzJhOTllYVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNjQuMSw1MS40N2gxOC43NmMtLjMxLDMuMS0xLjc1LDUuNTEtNC4zNCw3LjIxLTMuMzMsMS45My02LjY4LDEuOTUtMTAuMDMuMDUtMi42NC0xLjctNC4xLTQuMTItNC4zOS03LjI2WlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiNmZmZmZmZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiICBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFdPVyEgTG9vayBhdCB0aGF0LiBBbGwgeW91ciBjcmV3IGFyZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2hpcC1zaGFwZWQgYW5kIHRyYWluZWQgdG8gdGhlIGdpbGxzLlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgR3JlYXQgam9iLCBjYXB0YWluIVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgICB7IWV4Y2x1ZGVGaWx0ZXJzLmluY2x1ZGVzKCdvdmVyZHVlVG9nZ2xlJykgJiYgKFxyXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTQgaXRlbXMtY2VudGVyIHJvdW5kZWQtbGcgZ2FwLTQgeHM6Z2FwLTAgYmctYmFja2dyb3VuZCBib3JkZXIgYm9yZGVyLWJvcmRlciBwLTUgdGV4dC1jZW50ZXIgaG92ZXI6dGV4dC1saWdodC1ibHVlLXZpdmlkLTkwMCB3LWZ1bGxcIlxyXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHRvZ2dsZU92ZXJkdWVXcmFwcGVyKChwcmV2KSA9PiAhcHJldil9PlxyXG4gICAgICAgICAgICAgICAgICAgIHtvdmVyZHVlQm9vbGVhblxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICdWaWV3IGFsbCBjb21wbGV0ZWQgdHJhaW5pbmdzJ1xyXG4gICAgICAgICAgICAgICAgICAgICAgICA6ICdWaWV3IG92ZXJkdWUgdHJhaW5pbmdzJ31cclxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgKVxyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBDcmV3VHJhaW5pbmdMaXN0XHJcblxyXG5leHBvcnQgY29uc3QgT3ZlcmR1ZVRyYWluaW5nTGlzdCA9ICh7XHJcbiAgICB0cmFpbmluZ1Nlc3Npb25EdWVzLFxyXG4gICAgaXNWZXNzZWxWaWV3ID0gZmFsc2UsXHJcbiAgICBoaWRlQ3Jld0NvbHVtbiA9IGZhbHNlLFxyXG4gICAgcGFnZVNpemUgPSAyMCxcclxufTogYW55KSA9PiB7XHJcbiAgICBjb25zdCBpc1dpZGUgPSB1c2VNZWRpYVF1ZXJ5KCcobWluLXdpZHRoOiA3MjBweCknKVxyXG5cclxuICAgIGNvbnN0IGFsbENvbHVtbnMgPSBjcmVhdGVDb2x1bW5zKFtcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGFjY2Vzc29yS2V5OiAndGl0bGUnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdUcmFpbmluZycsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGRhdGE6IGFueSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIDxNb2JpbGVUcmFpbmluZ0NhcmQgZGF0YT17ZGF0YX0gdHlwZT1cIm92ZXJkdWVcIiAvPlxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ3Zlc3NlbCcsXHJcbiAgICAgICAgICAgIGNlbGxBbGlnbm1lbnQ6ICdsZWZ0JyxcclxuICAgICAgICAgICAgaGVhZGVyOiAnVmVzc2VsJyxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhbmRzY2FwZScsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICA8PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNWZXNzZWxWaWV3ID09IGZhbHNlICYmIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOnRhYmxlLWNlbGwgcC0yIGFsaWduLXRvcCBsZzphbGlnbi1taWRkbGUgaXRlbXMtY2VudGVyIHRleHQtbGVmdFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtkdWUudmVzc2VsPy50aXRsZSB8fCAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID0gcm93QT8ub3JpZ2luYWw/LnZlc3NlbD8udGl0bGUgfHwgJydcclxuICAgICAgICAgICAgICAgIGNvbnN0IHZhbHVlQiA9IHJvd0I/Lm9yaWdpbmFsPy52ZXNzZWw/LnRpdGxlIHx8ICcnXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gdmFsdWVBLmxvY2FsZUNvbXBhcmUodmFsdWVCKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgICBhY2Nlc3NvcktleTogJ2NyZXcnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdDcmV3JyxcclxuICAgICAgICAgICAgYnJlYWtwb2ludDogJ2xhcHRvcCcsXHJcbiAgICAgICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgICAgIGNvbnN0IGR1ZSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICAgICAgY29uc3QgbWVtYmVycyA9IGR1ZS5tZW1iZXJzIHx8IFtdXHJcblxyXG4gICAgICAgICAgICAgICAgcmV0dXJuIGlzV2lkZSA/IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMubWFwKChtZW1iZXI6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VG9vbHRpcCBrZXk9e21lbWJlci5pZH0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUb29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxBdmF0YXJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkdWUuc3RhdHVzLmlzT3ZlcmR1ZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnZGVzdHJ1Y3RpdmUnXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdzZWNvbmRhcnknXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z2V0Q3Jld0luaXRpYWxzKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVtYmVyLmZpcnN0TmFtZSxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lbWJlci5zdXJuYW1lLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwVHJpZ2dlcj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRvb2x0aXBDb250ZW50PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlci5maXJzdE5hbWV9eycgJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHttZW1iZXIuc3VybmFtZSA/PyAnJ31cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9Ub29sdGlwQ29udGVudD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1Rvb2x0aXA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgICAgICA8ZGl2XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAnIXJvdW5kZWQtZnVsbCBzaXplLTEwJyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGR1ZS5zdGF0dXM/LmNsYXNzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICApfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge21lbWJlcnMubGVuZ3RofVxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzb3J0aW5nRm46IChyb3dBOiBhbnksIHJvd0I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVBID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dBPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0E/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5zdXJuYW1lID8/ICcnfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgY29uc3QgdmFsdWVCID1cclxuICAgICAgICAgICAgICAgICAgICBgJHtyb3dCPy5vcmlnaW5hbD8ubWVtYmVycz8ubm9kZXM/LlswXT8uZmlyc3ROYW1lID8/ICcnfSAke3Jvd0I/Lm9yaWdpbmFsPy5tZW1iZXJzPy5ub2Rlcz8uWzBdPy5zdXJuYW1lID8/ICcnfWAgfHxcclxuICAgICAgICAgICAgICAgICAgICAnJ1xyXG4gICAgICAgICAgICAgICAgcmV0dXJuIHZhbHVlQS5sb2NhbGVDb21wYXJlKHZhbHVlQilcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgICAgIHtcclxuICAgICAgICAgICAgYWNjZXNzb3JLZXk6ICdzdGF0dXMnLFxyXG4gICAgICAgICAgICBjZWxsQWxpZ25tZW50OiAncmlnaHQnLFxyXG4gICAgICAgICAgICBoZWFkZXI6ICdTdGF0dXMnLFxyXG4gICAgICAgICAgICBicmVha3BvaW50OiAnbGFuZHNjYXBlJyxcclxuICAgICAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc3QgZHVlID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgJHtkdWUuc3RhdHVzPy5pc092ZXJkdWUgPyBkdWUuc3RhdHVzPy5jbGFzcyA6ICcnfSByb3VuZGVkLW1kIHctZml0ICFwLTIgdGV4dC1ub3dyYXBgfT5cclxuICAgICAgICAgICAgICAgICAgICAgICAge2R1ZS5zdGF0dXM/LmxhYmVsIHx8ICdVbmtub3duIFN0YXR1cyd9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICApXHJcbiAgICAgICAgICAgIH0sXHJcbiAgICAgICAgfSxcclxuICAgIF0pXHJcblxyXG4gICAgLy8gRmlsdGVyIG91dCBjcmV3IGNvbHVtbiB3aGVuIGhpZGVDcmV3Q29sdW1uIGlzIHRydWVcclxuICAgIGNvbnN0IGNvbHVtbnMgPSBoaWRlQ3Jld0NvbHVtblxyXG4gICAgICAgID8gYWxsQ29sdW1ucy5maWx0ZXIoKGNvbDogYW55KSA9PiBjb2wuYWNjZXNzb3JLZXkgIT09ICdjcmV3JylcclxuICAgICAgICA6IGFsbENvbHVtbnNcclxuXHJcbiAgICByZXR1cm4gKFxyXG4gICAgICAgIDw+XHJcbiAgICAgICAgICAgIHt0cmFpbmluZ1Nlc3Npb25EdWVzPy5sZW5ndGggPiAwID8gKFxyXG4gICAgICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICAgICAgZGF0YT17dHJhaW5pbmdTZXNzaW9uRHVlc31cclxuICAgICAgICAgICAgICAgICAgICBwYWdlU2l6ZT17cGFnZVNpemV9XHJcbiAgICAgICAgICAgICAgICAgICAgc2hvd1Rvb2xiYXI9e2ZhbHNlfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZ3JvdXAgYm9yZGVyLWIgaG92ZXI6IGB9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IGNvbC1zcGFuLTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgZ2FwLTIgcC0yIHB0LTRcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzdmdcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCIhdy1bNzVweF0gaC1hdXRvXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aWV3Qm94PVwiMCAwIDE0NyAxNDcuMDFcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTcyLjQ1LDBjMTcuMjYtLjA3LDMyLjY4LDUuMTIsNDYuMjksMTUuNTYsMTAuNiw4LjM5LDE4LjM4LDE4Ljg4LDIzLjM1LDMxLjQ3LDUuMDgsMTMuNDUsNi4yMSwyNy4yMywzLjQxLDQxLjM0LTMuMjMsMTUuMDgtMTAuMzgsMjcuOTItMjEuNDQsMzguNTItMTIuMjIsMTEuNDItMjYuNjksMTguMDEtNDMuNDQsMTkuNzgtMTUuNjYsMS40Mi0zMC4zMS0xLjc1LTQzLjk1LTkuNTItMTMuMTEtNy43My0yMi45OC0xOC40NC0yOS42MS0zMi4xM0MuOSw5MS44Mi0xLjIyLDc3Ljk4LjY3LDYzLjUxYzIuMzYtMTYuMTIsOS4xNy0yOS45OCwyMC40NC00MS41OEMzMy4yNSw5Ljc4LDQ3LjkxLDIuNjMsNjUuMDguNDljMi40Ni0uMjcsNC45MS0uNDMsNy4zNy0uNDlaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiNmZmZmZmZcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTcyLjQ1LDBjMTcuMjYtLjA3LDMyLjY4LDUuMTIsNDYuMjksMTUuNTYsMTAuNiw4LjM5LDE4LjM4LDE4Ljg4LDIzLjM1LDMxLjQ3LDUuMDgsMTMuNDUsNi4yMSwyNy4yMywzLjQxLDQxLjM0LTMuMjMsMTUuMDgtMTAuMzgsMjcuOTItMjEuNDQsMzguNTItMTIuMjIsMTEuNDItMjYuNjksMTguMDEtNDMuNDQsMTkuNzgtMTUuNjYsMS40Mi0zMC4zMS0xLjc1LTQzLjk1LTkuNTItMTMuMTEtNy43My0yMi45OC0xOC40NC0yOS42MS0zMi4xM0MuOSw5MS44Mi0xLjIyLDc3Ljk4LjY3LDYzLjUxYzIuMzYtMTYuMTIsOS4xNy0yOS45OCwyMC40NC00MS41OEMzMy4yNSw5Ljc4LDQ3LjkxLDIuNjMsNjUuMDguNDljMi40Ni0uMjcsNC45MS0uNDMsNy4zNy0uNDlaTTgyLjQ5LDE5LjQ2Yy0yLjAxLTEuMS00LjE0LTEuODUtNi4zOS0yLjI2LTEuNDItLjE1LTIuODQtLjM1LTQuMjUtLjYxLTEuNDYtLjI2LTIuNzktLjgxLTQuMDEtMS42M2wtLjM1LS4zNWMtLjI5LS41My0uNi0xLjA0LS45My0xLjU0LS4wOS43LS4xNiwxLjQxLS4yMSwyLjEyLjAzLjQuMDguOC4xNiwxLjE5LjEzLjQ0LjI3Ljg4LjQ0LDEuMzEtLjUtLjYxLS44Ni0xLjI5LTEuMS0yLjA1LS4wOC0uNC0uMTctLjc4LS4yOC0xLjE3LTEuNzIuOTItMi43MywyLjM2LTMuMDMsNC4yOS0uMTUsMS4zLS4wNywyLjU5LjI2LDMuODUtLjAxLDAtLjAzLjAxLS4wNS4wMi0xLjItLjU4LTIuMjUtMS4zOC0zLjE1LTIuMzgtLjM1LS40MS0uNy0uODMtMS4wMy0xLjI2LTMuNjUsNC43MS00LjU4LDkuOTItMi44LDE1LjYzLjIyLjY3LjQ4LDEuMzIuNzcsMS45Ni0uODguOS0xLjMyLDEuOTktMS4zMSwzLjI3LjA3LDIuNDYuMDYsNC45MS0uMDUsNy4zNywwLC43My4xNSwxLjQxLjQ5LDIuMDUuNS42NiwxLjE0Ljg0LDEuOTEuNTEuMDQsMS4wOC4xNCwyLjE1LjI4LDMuMjIuMzIsMS42LjkxLDMuMDksMS43Nyw0LjQ4LDEuMDIsMS42OSwyLjMsMy4xNywzLjgzLDQuNDMuMDMsMi41NS0uMjEsNS4wNy0uNzUsNy41Ni0uMjUsMS4wOC0uNiwyLjEyLTEuMDcsMy4xMy0uMDYtLjgyLS4wOC0xLjY1LS4wNy0yLjQ3LTMuNTEsMS4wNi03LjAzLDIuMTMtMTAuNTUsMy4yLS4wNS4xOC0uMDUuMzUsMCwuNTQtMywxLjAzLTUuNzUsMi41LTguMjYsNC40MS0yLjQ5LDEuOTUtNC4yOSw0LjQxLTUuMzksNy40LTEuNDQsMy43LTIuNDgsNy41MS0zLjEzLDExLjQzLS44NSw1LjEzLTEuMzksMTAuMjktMS41OSwxNS40OS0uMjgsNi44OC0uMjcsMTMuNzUuMDUsMjAuNjItMTEuODUtOC4xOS0yMC41Ni0xOC45NC0yNi4xMy0zMi4yNEMxLjA2LDg3LjE5LS4yMiw3My4wMywyLjc3LDU4LjQ3YzMuNDEtMTUuMywxMC44Ni0yOC4yMSwyMi4zNy0zOC43MUMzNy41Myw4Ljc3LDUyLjA1LDIuNjQsNjguNjgsMS4zOGMxNi4zMS0uOTYsMzEuMjcsMy4wMyw0NC44OSwxMS45NSwxMi43Nyw4LjY1LDIxLjk1LDIwLjE3LDI3LjU1LDM0LjU1LDUuMSwxMy43NSw2LjAzLDI3Ljc4LDIuOCw0Mi4wOS0zLjY2LDE1LjA4LTExLjI1LDI3LjczLTIyLjc5LDM3Ljk2LTIuMTcsMS44OC00LjQzLDMuNjMtNi43OSw1LjI1LjItNS4yNS4yNi0xMC41MS4xOS0xNS43Ny0uMDgtNi4zLS41OC0xMi41Ny0xLjQ5LTE4LjgtLjYxLTQuMTctMS42NC04LjIzLTMuMDgtMTIuMTgtLjYzLTEuNy0xLjQzLTMuMy0yLjQzLTQuODEtMS43Mi0yLjItMy44LTMuOTgtNi4yMy01LjM0LTEuNy0uOTctMy40Ny0xLjc4LTUuMzItMi40MywwLS4xNywwLS4zNC0uMDUtLjUxLTMuNTEtMS4wNy03LjAzLTIuMTQtMTAuNTUtMy4yLDAsLjY3LDAsMS4zNC0uMDIsMi4wMS0uNzEtMS42MS0xLjE4LTMuMjktMS40LTUuMDQtLjI4LTEuOTItLjQtMy44NS0uMzctNS43OSwzLjUxLTMuMDUsNS4zOC02LjksNS42LTExLjU3LDEuMDkuNDMsMS44NS4xMSwyLjI5LS45OC4xNC0uMzYuMjMtLjc0LjI4LTEuMTIuMTYtMi43MS4zOS01LjQyLjY4LTguMTIuMDItMS4xNi0uMzUtMi4xNi0xLjEyLTMuMDEuNzItMiwuOTgtNC4wNi43Ny02LjE4LS4yMy0zLjAyLS45OS01LjktMi4yOS04LjYzLS4yNS0uNDktLjYtLjg5LTEuMDUtMS4xOS0uOS0uNTctMS44NS0xLjA1LTIuODUtMS40NS0yLjMyLS45My00LjY2LTEuNjktNy0yLjI5bDIuOTQsMi4xYy4yMy4xOS40NC4zOC42NS41OFpNNjcuNzksMTYuNDNjMS41Ny44MiwzLjIzLDEuMzMsNC45OSwxLjU2LDMuNjQuMTcsNywxLjIxLDEwLjA4LDMuMTMuNDYuMzIuOTEuNjQsMS4zNS45OC41MS41LDEuMDQuOTgsMS41OSwxLjQyLS4xNi0uNzktLjM3LTEuNTgtLjYzLTIuMzgtLjItLjQ1LS40NC0uODgtLjcyLTEuMjgsMS4xNy4zNywyLjI5Ljg3LDMuMzYsMS40OS41MS4zLjg4LjczLDEuMSwxLjI4LDEuNDksMy4zNSwyLjE0LDYuODUsMS45NiwxMC41LS4xLDEuNTYtLjU4LDMtMS40NSw0LjI5LjE4LTMuMTMtLjk5LTUuNTktMy41Mi03LjQtLjA4LS4wMy0uMTUtLjAzLS4yMywwLTQuMDcsMS4yNC04LjIzLDIuMS0xMi40NiwyLjU3LTIuMTMuMjMtNC4yNi4yMS02LjM5LS4wNS0xLjM2LS4xNy0yLjYtLjY0LTMuNzMtMS40LS4yMS0uMTYtLjQtLjM0LS41OC0uNTQtLjE5LS4yNi0uMzgtLjUtLjU4LS43NS0xLjY0Ljk1LTIuNzksMi4zMi0zLjQzLDQuMTEtLjMuODUtLjUsMS43Mi0uNjEsMi42MS0xLjQxLTIuODYtMS45Ny01Ljg4LTEuNjgtOS4wNS4yOS0yLjM4LDEuMTEtNC41NiwyLjQ1LTYuNTMsMS4wMSwxLjEzLDIuMiwyLjA0LDMuNTUsMi43My43OC4zMSwxLjU5LjUsMi40My41OC0uNDEtLjk4LS43LTEuOTktLjg2LTMuMDMtLjItMS4xOC0uMTEtMi4zMy4yOC0zLjQ1LjIxLS40OS40OS0uOTIuODQtMS4zMS43LDEuODMsMS45NSwzLjEzLDMuNzYsMy45LjgzLjI4LDEuNjcuNTEsMi41Mi43LS41LS41NC0xLjAxLTEuMDctMS41Mi0xLjYxLS44Mi0uOS0xLjQzLTEuOTMtMS44NC0zLjA4Wk01OS4wNiwzNy4zOGMuMDItMS44OS42MS0zLjU5LDEuNzUtNS4wOS4yNy0uMjcuNTQtLjU0LjgyLS43OS45NS45MSwyLjA3LDEuNTQsMy4zNiwxLjg5LDEuNjIuNDIsMy4yNy42MSw0Ljk1LjU4LDIuNTctLjA1LDUuMTItLjMsNy42NS0uNzcsMi42OS0uNDgsNS4zNC0xLjExLDcuOTYtMS44OSwxLjk5LDEuNTcsMi44NiwzLjYyLDIuNjQsNi4xNi0xLjc3LTEuNzUtMy45LTIuNTEtNi4zOS0yLjI2LS42NC4wNC0xLjI4LjEyLTEuOTEuMjMtNC4yMS4wMy04LjQzLjAzLTEyLjY1LDAtMS4zNi0uMjYtMi43My0uMzItNC4xMS0uMTktMS41Ny4zMi0yLjkyLDEuMDItNC4wNiwyLjEyWk03MC42MywzNi42OGMxLjk0LS4wNiwzLjg4LS4wNiw1LjgzLS4wMi0uNjUuNDEtMS4xNC45Ni0xLjQ3LDEuNjYtLjMyLS41NS0uOC0uODYtMS40Mi0uOTMtLjI3LDAtLjUyLjA3LS43NS4yMS0uMjguMjEtLjUxLjQ1LS43LjcyLS4zNC0uNy0uODQtMS4yNC0xLjQ5LTEuNjNaTTkwLjY1LDM3Ljc1cy4wOCwwLC4xMi4wNWMuNC43MS41NCwxLjQ3LjQyLDIuMjktLjI4LDIuNDgtLjUsNC45Ny0uNjUsNy40Ny0uMDQuMzktLjE3Ljc1LS4zNywxLjA3LS4wNS4wNi0uMTIuMS0uMTkuMTQtLjI4LS4xMi0uNTQtLjI4LS43NS0uNTEtLjAzLS45Mi0uMDMtMS44MywwLTIuNzUuNzctMS42My45NS0zLjMzLjU2LTUuMDktLjEtLjM4LS4yMy0uNzYtLjQtMS4xMi40OC0uNDcuOS0uOTgsMS4yNi0xLjU0Wk01Ny4wNiwzNy44Yy4wNy4wMi4xMy4wNy4xNi4xNC4xNC4yOC4yOS41NC40Ny43OS4wMy4yMy4wMy40NywwLC43LS42NCwxLjY3LS43LDMuMzctLjE5LDUuMDksMCwxLjI0LjAzLDIuNDcuMDcsMy43MS0uMDEuMDctLjAzLjE0LS4wNS4yMS0uMTguMTQtLjM4LjI1LS42MS4zMy0uMTYtLjA2LS4yNi0uMTYtLjMtLjMzLS4xNC0uMzktLjIxLS44LS4yMS0xLjIxLjEtMi40LjEyLTQuODEuMDUtNy4yMS0uMDMtLjgxLjE4LTEuNTQuNjEtMi4yMlpNNzMuNDgsMzguNTljLjE0LDAsLjI2LjA3LjM1LjE5LjM3LjUyLjYzLDEuMS43OSwxLjczLjM1LDIuODcsMS42MSw1LjI2LDMuNzYsNy4xNiwyLjg0LDIuMjEsNS43NywyLjMyLDguNzcuMzMuMjgtLjIyLjU2LS40Ny44Mi0uNzIuNDEsNi41MS0yLjEzLDExLjQ4LTcuNjMsMTQuOTEtMy4yNCwxLjY4LTYuNjYsMi4yMS0xMC4yNywxLjYxLTIuMzctLjQ3LTQuNDMtMS41LTYuMjEtMy4xLTEuODctMS42OC0zLjI5LTMuNjktNC4yNy02LS40OC0xLjI5LS43My0yLjYzLS43NS00LjAxLS4wOC0xLjI5LS4xMS0yLjU4LS4wOS0zLjg3LDEuNjgsMS45NCwzLjgsMi43OCw2LjM3LDIuNTQsMS44LS4zNSwzLjMxLTEuMiw0LjU1LTIuNTQsMS41NS0xLjcxLDIuNDgtMy43MiwyLjgtNi4wMi4xNi0uODIuNDktMS41NSwxLTIuMTlaTTY0LjEsNTEuNDdoMTguNzZjLS4zMSwzLjEtMS43NSw1LjUxLTQuMzQsNy4yMS0zLjMzLDEuOTMtNi42OCwxLjk1LTEwLjAzLjA1LTIuNjQtMS43LTQuMS00LjEyLTQuMzktNy4yNlpNODIuMyw2Mi4yOXMuMDYuMDUuMDcuMDljLjAyLDIuOC4zOSw1LjU2LDEuMTIsOC4yNi4zNywxLjI4LjkyLDIuNDYsMS42NiwzLjU1LS4zOCwzLjAzLTEuMzQsNS44Ni0yLjg3LDguNDktMS45NywzLjE1LTQuNzksNS4wNC04LjQ3LDUuNjctMi41Ni0uMTktNC44LTEuMTItNi43Mi0yLjgtMS44NC0xLjc2LTMuMTktMy44NS00LjA0LTYuMjgtLjU2LTEuNTYtLjk1LTMuMTctMS4xNy00LjgxLjQ5LS42Ljg4LTEuMjcsMS4xNy0yLjAxLjc0LTEuOTQsMS4yLTMuOTUsMS40LTYuMDIuMTMtMS4xNi4yLTIuMzMuMjMtMy41LjAzLS4wNC4wNy0uMDUuMTItLjAyLDEuOTUsMS4zLDQuMDksMi4wNSw2LjQ0LDIuMjQsMy4zMS4yOSw2LjQ1LS4zLDkuNDMtMS43Ny41OC0uMzIsMS4xMi0uNjksMS42My0xLjFaTTk1LjgzLDc1LjA4YzIuODksMS4wMyw1LjUzLDIuNDksNy45Myw0LjM2LDEuNzMsMS4zOSwzLjA3LDMuMDcsNC4wNCw1LjA2LDEuNDcsMy4yNSwyLjU2LDYuNjIsMy4yNywxMC4xMy45OCw0Ljg3LDEuNjIsOS43OCwxLjkxLDE0Ljc0LjUxLDguMjMuNTMsMTYuNDYuMDUsMjQuNjgtMTMuNzIsOC44MS0yOC43MywxMi42Ni00NS4wNSwxMS41NS0xMi4zMy0uOTktMjMuNjYtNC44NC0zMy45OS0xMS41NS0uNDMtOC4zMS0uNC0xNi42Mi4wOS0yNC45Mi4zLTQuOTguOTUtOS45MSwxLjk2LTE0Ljc5LjY2LTMuMiwxLjY0LTYuMjksMi45NC05LjI5Ljg3LTIuMDMsMi4xNC0zLjc2LDMuOC01LjIsMi40OC0yLjA4LDUuMjctMy42Niw4LjM1LTQuNzQuNiw2Ljc1LjIxLDEzLjQzLTEuMTQsMjAuMDYtLjQxLDIuMTQtLjk1LDQuMjQtMS42Myw2LjMtLjM4LDEuMDgtLjg5LDIuMS0xLjU0LDMuMDMtLjI4LjMzLS42LjYtLjk2LjgyLS4xNi4wOC0uMzQuMTMtLjUxLjE2djE2LjhoNTYuMjd2LTE2LjhjLS41OC0uMTUtMS4wNS0uNDYtMS40Mi0uOTMtLjctLjk5LTEuMjUtMi4wNi0xLjYzLTMuMjItLjc0LTIuMjYtMS4zMS00LjU2LTEuNzMtNi45MS0xLTQuOTktMS40MS0xMC4wMy0xLjIxLTE1LjEyLjA0LTEuNDIuMTEtMi44My4yMS00LjI1WlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDUyMzUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbFJ1bGU9XCJldmVub2RkXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb3BhY2l0eT1cIi45N1wiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNjMuNzgsMzUuNzRjMS4xNCwwLDIuMjguMSwzLjQxLjI4di42MWMxLjc2LS4zNywzLjE3LjE1LDQuMjIsMS41OS4xNi4yNy4yOC41Ni4zNS44Ni0uMTcuNDktLjMzLjk4LS40NywxLjQ3LjE4LjA4LjM2LjEzLjU2LjE0LS4zOCwyLjk5LTEuOCw1LjM0LTQuMjUsNy4wNy0yLjY4LDEuNTYtNS4yMywxLjM3LTcuNjUtLjU2LTEuNjQtMS41My0yLjM3LTMuNDItMi4xNy01LjY3LjE0LTEuNTkuODEtMi45MiwxLjk4LTMuOTksMS4xNi0xLDIuNS0xLjYsNC4wMS0xLjhaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMyOTk4ZTlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTgyLjA3LDM1Ljc0YzIuNDEtLjEzLDQuNDEuNzEsNiwyLjUyLDEuMjcsMS43MSwxLjY1LDMuNjEsMS4xMiw1LjY5LS43MSwyLjM5LTIuMjUsMy45My00LjY0LDQuNjQtMS4zNS4zNS0yLjY4LjI2LTMuOTctLjI4LTEuODMtLjg5LTMuMjMtMi4yMy00LjE4LTQuMDQtLjY1LTEuMTktMS4wMy0yLjQ3LTEuMTQtMy44My4xOS0uMDIuMzctLjA2LjU2LS4wOS0uMTEtLjQ1LS4yNS0uOS0uNDItMS4zMy4yMy0uODMuNzItMS40NywxLjQ1LTEuOTEuMy0uMTguNjEtLjM0LjkzLS40Ny43MS0uMDIsMS40My0uMDMsMi4xNS0uMDJ2LS42MWMuNzItLjExLDEuNDQtLjIsMi4xNS0uMjhaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMyOTk4ZTlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTY1LjU1LDQwLjZjLjk3LDAsMS40NS40OCwxLjQyLDEuNDUtLjIzLjc1LS43MywxLjA3LTEuNTIuOTYtLjY2LS4yNy0uOTUtLjc2LS44Ni0xLjQ3LjE2LS40OC40OC0uNzkuOTYtLjkzWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDI0NDUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwcHhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk04MS4xOCw0MC42Yy43LS4wNCwxLjE4LjI4LDEuNDIuOTMuMDYsMS4wOC0uNDUsMS41Ny0xLjUyLDEuNDctLjgxLS4zNy0xLjA1LS45Ny0uNzItMS44LjIxLS4zLjQ4LS41LjgyLS42MVpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzA1MjQ1MVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNjIuODQsNTAuMjVoMjEuMjNjLjEsMy43OC0xLjM1LDYuOC00LjM0LDkuMDgtMywyLjAzLTYuMjMsMi41MS05LjcxLDEuNDUtMy42NS0xLjM1LTUuOTYtMy45MS02LjkzLTcuNjgtLjE4LS45NC0uMjctMS44OS0uMjYtMi44NVpNNjQuMSw1MS40N2MuMjksMy4xNCwxLjc1LDUuNTYsNC4zOSw3LjI2LDMuMzUsMS45LDYuNywxLjg5LDEwLjAzLS4wNSwyLjU5LTEuNyw0LjAzLTQuMTEsNC4zNC03LjIxaC0xOC43NlpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzA1MjI1MFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMHB4XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzMuMiw4OS41NGMuMTkuMDYuMzcuMDYuNTYsMCw0LjM2LS42Nyw3LjYzLTIuOTEsOS44Mi02LjcyLDEuNDktMi43OCwyLjQzLTUuNzMsMi44LTguODdsLjIxLTIuMjRjMi43Ljg1LDUuNCwxLjY4LDguMTIsMi40Ny0uMjksMy44MS0uMzYsNy42Mi0uMjEsMTEuNDMuMzMsNC40NCwxLjAyLDguODMsMi4wNSwxMy4xNi40NiwxLjkxLDEuMTIsMy43NSwyLjAxLDUuNTEuMy41NC42NywxLjAzLDEuMSwxLjQ3LjIyLjIxLjQ4LjM5Ljc1LjU0djE0Ljc5aC01My44NXYtMTQuNzljLjU0LS4zLjk4LS43LDEuMzMtMS4yMS41Ni0uODUsMS4wMy0xLjc1LDEuNC0yLjcxLjk3LTIuNzUsMS42OC01LjU3LDIuMTUtOC40NS45NS01LjEyLDEuMzEtMTAuMjgsMS4wNy0xNS40OS0uMDQtMS4zNi0uMTMtMi43My0uMjYtNC4wOC4wMS0uMDYuMDMtLjExLjA1LS4xNiwyLjY5LS44Myw1LjM4LTEuNjYsOC4wNy0yLjQ3LjE2LDMuMzYuOTEsNi41OCwyLjI2LDkuNjYsMS4yNSwyLjc3LDMuMTUsNC45Niw1LjcyLDYuNTYsMS41MS44NiwzLjEzLDEuNCw0Ljg1LDEuNjFaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMyOTk4ZTlcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBweFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTQ1LjM0LDEyNS44aDIzLjg0djYuNjNoLTIzLjg0di02LjYzWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDUyMzUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzAuMTcsMTI1LjhoNi41OHY2LjYzaC02LjU4di02LjYzWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMDUyMjUwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzcuNzcsMTI1LjhoMjMuODR2Ni42M2gtMjMuODR2LTYuNjNaXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbD1cIiMwNTIzNTBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGhcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZD1cIk02Ny45OCwxMjcuMDF2NC4yaC0yMS40MnYtNC4yaDIxLjQyWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMmE5OWVhXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzUuNTgsMTI3LjAxdjQuMmgtNC4ydi00LjJoNC4yWlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCIjMmE5OWVhXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3Ryb2tlV2lkdGg9XCIwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNNzguOTksMTI3LjAxaDIxLjQydjQuMmgtMjEuNDJ2LTQuMlpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiIzJhOTllYVwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTY0LjEsNTEuNDdoMTguNzZjLS4zMSwzLjEtMS43NSw1LjUxLTQuMzQsNy4yMS0zLjMzLDEuOTMtNi42OCwxLjk1LTEwLjAzLjA1LTIuNjQtMS43LTQuMS00LjEyLTQuMzktNy4yNlpcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWxsPVwiI2ZmZmZmZlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPVwiMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiICBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBXT1chIExvb2sgYXQgdGhhdC4gQWxsIHlvdXIgY3JldyBhcmUgc2hpcC1zaGFwZWRcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmQgdHJhaW5lZCB0byB0aGUgZ2lsbHMuIEdyZWF0IGpvYiwgY2FwdGFpbiFcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICA8Lz5cclxuICAgIClcclxufVxyXG4iXSwibmFtZXMiOlsiVFJBSU5JTkdfU0VTU0lPTlMiLCJSRUFEX1RSQUlOSU5HX1NFU1NJT05fRFVFUyIsInVzZUxhenlRdWVyeSIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUcmFpbmluZ0xpc3RGaWx0ZXIiLCJHZXRUcmFpbmluZ1Nlc3Npb25TdGF0dXMiLCJmb3JtYXQiLCJnZXRQZXJtaXNzaW9ucyIsImhhc1Blcm1pc3Npb24iLCJmb3JtYXREYXRlIiwiZGF0ZVN0cmluZyIsImRhdGUiLCJEYXRlIiwiTG9hZGluZyIsInVzZVJvdXRlciIsImNyZWF0ZUNvbHVtbnMiLCJEYXRhVGFibGUiLCJEYXRhVGFibGVTb3J0SGVhZGVyIiwiTG9hZGVyMiIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQ2FyZCIsImdldENyZXdJbml0aWFscyIsIlAiLCJUb29sdGlwIiwiVG9vbHRpcENvbnRlbnQiLCJUb29sdGlwVHJpZ2dlciIsInVzZU1lZGlhUXVlcnkiLCJjbiIsInVzZVRyYWluaW5nRmlsdGVycyIsInVzZVF1ZXJ5U3RhdGUiLCJMb2NhdGlvbk1vZGFsIiwidXNlVmVzc2VsSWNvbkRhdGEiLCJNb2JpbGVUcmFpbmluZ0NhcmQiLCJDcmV3VHJhaW5pbmdMaXN0IiwibWVtYmVySWQiLCJ2ZXNzZWxJZCIsImFwcGx5RmlsdGVyUmVmIiwiZXhjbHVkZUZpbHRlcnMiLCJyb3V0ZXIiLCJsaW1pdCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsInBhZ2VJbmZvIiwic2V0UGFnZUluZm8iLCJ0b3RhbENvdW50IiwiaGFzTmV4dFBhZ2UiLCJoYXNQcmV2aW91c1BhZ2UiLCJ0cmFpbmluZ0xpc3QiLCJzZXRUcmFpbmluZ0xpc3QiLCJ0cmFpbmluZ1Nlc3Npb25EdWVzIiwic2V0VHJhaW5pbmdTZXNzaW9uRHVlcyIsInBhZ2UiLCJzZXRQYWdlIiwidmVzc2VsSWRPcHRpb25zIiwic2V0VmVzc2VsSWRPcHRpb25zIiwidHJhaW5pbmdUeXBlSWRPcHRpb25zIiwic2V0VHJhaW5pbmdUeXBlSWRPcHRpb25zIiwidHJhaW5lcklkT3B0aW9ucyIsInNldFRyYWluZXJJZE9wdGlvbnMiLCJjcmV3SWRPcHRpb25zIiwic2V0Q3Jld0lkT3B0aW9ucyIsImlzVmVzc2VsVmlldyIsIm92ZXJkdWVTd2l0Y2hlciIsInNldE92ZXJkdWVTd2l0Y2hlciIsImlzV2lkZSIsImdldFZlc3NlbFdpdGhJY29uIiwibG9hZGluZyIsInZlc3NlbERhdGFMb2FkaW5nIiwib3ZlcmR1ZUJvb2xlYW4iLCJzZXRPdmVyZHVlQm9vbGVhbiIsInRvZ2dsZU92ZXJkdWVXcmFwcGVyIiwidmFsdWUiLCJwcmV2IiwibmV3VmFsdWUiLCJxdWVyeVRyYWluaW5nTGlzdCIsInRyYWluaW5nTGlzdExvYWRpbmciLCJmZXRjaFBvbGljeSIsIm9uQ29tcGxldGVkIiwicmVzcG9uc2UiLCJkYXRhIiwicmVhZFRyYWluaW5nU2Vzc2lvbnMiLCJub2RlcyIsInRyYW5zZm9ybWVkRGF0YSIsIm1hcCIsIml0ZW0iLCJjb21wbGV0ZVZlc3NlbERhdGEiLCJ2ZXNzZWwiLCJpZCIsInZlc3NlbElEcyIsIkFycmF5IiwiZnJvbSIsIlNldCIsImZpbHRlciIsInRyYWluaW5nVHlwZUlEcyIsImZsYXRNYXAiLCJ0cmFpbmluZ1R5cGVzIiwidCIsInRyYWluZXJJRHMiLCJ0cmFpbmVySUQiLCJtZW1iZXJJRHMiLCJtZW1iZXJzIiwib25FcnJvciIsImVycm9yIiwiY29uc29sZSIsImxvYWRUcmFpbmluZ0xpc3QiLCJzdGFydFBhZ2UiLCJzZWFyY2hGaWx0ZXIiLCJ2YXJpYWJsZXMiLCJvZmZzZXQiLCJsb2FkVHJhaW5pbmdTZXNzaW9uRHVlcyIsImR1ZUZpbHRlciIsIm1lbWJlcklEIiwiZXEiLCJ2ZXNzZWxJRCIsInRyYWluaW5nVHlwZUlEIiwiY29udGFpbnMiLCJkdWVEYXRlIiwibmUiLCJyZWFkVHJhaW5pbmdTZXNzaW9uRHVlcyIsImhhbmRsZU5hdmlnYXRpb25DbGljayIsIm5ld1BhZ2UiLCJzZXRGaWx0ZXIiLCJoYW5kbGVGaWx0ZXJDaGFuZ2UiLCJpbml0aWFsRmlsdGVyIiwibG9hZExpc3QiLCJsb2FkRHVlcyIsInRvZ2dsZU92ZXJkdWUiLCJjdXJyZW50IiwiYXBwbHkiLCJvdmVyZHVlIiwic2V0T3ZlcmR1ZSIsInRyYWluaW5nU2Vzc2lvbkR1ZXNMb2FkaW5nIiwiZmlsdGVyZWREYXRhIiwic2VhTG9nc01lbWJlcnMiLCJzb21lIiwibSIsImR1ZVdpdGhTdGF0dXMiLCJkdWUiLCJzdGF0dXMiLCJncm91cGVkRHVlcyIsInJlZHVjZSIsImFjYyIsImtleSIsInRyYWluaW5nVHlwZSIsInRyYWluaW5nTG9jYXRpb25UeXBlIiwidHJhaW5pbmdTZXNzaW9uIiwicHVzaCIsIm1lbWJlciIsIm1lcmdlZER1ZXMiLCJPYmplY3QiLCJ2YWx1ZXMiLCJncm91cCIsIm1lcmdlZE1lbWJlcnMiLCJleGlzdGluZ01lbWJlciIsImZpbmQiLCJmaXJzdE5hbWUiLCJzdXJuYW1lIiwiZiIsInBlcm1pc3Npb25zIiwic2V0UGVybWlzc2lvbnMiLCJ0cmFuc2Zvcm1Db21wbGV0ZWRUcmFpbmluZ3MiLCJ0cmFpbmluZyIsInRpdGxlIiwidHJhaW5lciIsImxhYmVsIiwiaXNPdmVyZHVlIiwiY2xhc3MiLCJkdWVXaXRoaW5TZXZlbkRheXMiLCJwcmlvcml0eSIsImNyZWF0ZVVuaWZpZWRUcmFpbmluZ0RhdGEiLCJ0cmFuc2Zvcm1lZENvbXBsZXRlZCIsInByaW9yaXRpemVkRHVlcyIsImFsbFRyYWluaW5ncyIsInNvcnQiLCJhIiwiYiIsImRhdGVBIiwiZ2V0VGltZSIsImRhdGVCIiwiaXNVbmlmaWVkRGF0YUxvYWRpbmciLCJpbmNsdWRlcyIsImdldFVuaWZpZWRUcmFpbmluZ0RhdGEiLCJ0cmFuc2Zvcm1lZENvbXBsZXRlZFRyYWluaW5nIiwidHJhbnNmb3JtVHJhaW5pbmdMaXN0VG9PdmVyZHVlRm9ybWF0IiwiZXJyb3JNZXNzYWdlIiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY29sdW1uIiwiY2VsbCIsInJvdyIsIm9yaWdpbmFsIiwidHlwZSIsInNvcnRpbmdGbiIsInJvd0EiLCJyb3dCIiwiY2VsbEFsaWdubWVudCIsImJyZWFrcG9pbnQiLCJzcGFuIiwidmFsdWVBIiwidmFsdWVCIiwibG9jYWxlQ29tcGFyZSIsImRpdiIsImNsYXNzTmFtZSIsImljb25DbGFzc05hbWUiLCJzaXplIiwidmFyaWFudCIsImluZGV4Iiwib25DaGFuZ2UiLCJPdmVyZHVlVHJhaW5pbmdMaXN0IiwiaGlkZUNyZXdDb2x1bW4iLCJwYWdlU2l6ZSIsImxlbmd0aCIsInNob3dUb29sYmFyIiwic3ZnIiwieG1sbnMiLCJ2aWV3Qm94IiwicGF0aCIsImQiLCJmaWxsIiwic3Ryb2tlV2lkdGgiLCJmaWxsUnVsZSIsIm9wYWNpdHkiLCJwIiwiYnV0dG9uIiwib25DbGljayIsImFsbENvbHVtbnMiLCJjb2wiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/list.tsx\n"));

/***/ })

});