'use client'

import Link from 'next/link'
import { format } from 'date-fns'
import {
    Avatar,
    AvatarFallback,
    Badge,
    Button,
    getCrewInitials,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Separator,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { Label } from '@/components/ui/label'
import { LocationModal } from '../../vessels/list'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { cn } from '@/app/lib/utils'

// Helper function to format dates using date-fns
const formatDate = (dateString: any) => {
    if (!dateString) return ''
    try {
        const date = new Date(dateString)
        return format(date, 'dd/MM/yy')
    } catch {
        return ''
    }
}

interface MobileTrainingCardProps {
    data: any
    memberId?: number
    type?: 'completed' | 'overdue' | 'upcoming'
}

export const MobileTrainingCard = ({
    data,
    memberId,
    type = 'completed',
}: MobileTrainingCardProps) => {
    const bp = useBreakpoints()

    // Handle unified data structure
    const training = data
    const isCompleted =
        type === 'completed' || training.status?.label === 'Completed'
    const isOverdue = training.status?.isOverdue || false
    const isUpcoming = training.status?.dueWithinSevenDays || false

    // Members are now consistently in the same structure
    const members = training.members || []

    // Training title handling for both completed and overdue/upcoming
    const trainingTitle =
        training.trainingType?.title ||
        training.trainingTypes?.nodes
            ?.map((item: any) => item.title)
            .join(', ') ||
        ''

    return (
        <div className="w-full space-y-3 tablet-md:border-none border-b border-border py-3 small:pe-4">
            <div className="flex flex-wrap justify-between items-center">
                {isCompleted ? (
                    <Link
                        href={`/crew-training/info?id=${training.id}`}
                        className="font-semibold text-base hover:text-primary">
                        {formatDate(training.date)}
                    </Link>
                ) : (
                    <div className="font-semibold text-base">
                        {trainingTitle}
                    </div>
                )}
                <div className="flex gap-2 items-center landscape:hidden">
                    <Label className="text-sm m-0 text-muted-foreground">
                        {isCompleted ? 'Trainer:' : 'Status:'}
                    </Label>
                    {isCompleted ? (
                        <div className="text-sm font-medium">
                            {memberId && training.trainer?.id === +memberId
                                ? 'You'
                                : `${(training.trainer && training.trainer.firstName) || ''} ${(training.trainer && training.trainer.surname) || ''}`}
                        </div>
                    ) : (
                        <div
                            className={cn(
                                'text-sm font-medium px-2 py-1 rounded-md',
                                isOverdue &&
                                    'bg-destructive/10 text-destructive',
                                isUpcoming && 'bg-warning/10 text-warning',
                                !isOverdue &&
                                    !isUpcoming &&
                                    'bg-muted text-muted-foreground',
                            )}>
                            {training.status?.label || 'Unknown'}
                        </div>
                    )}
                </div>
            </div>

            <div
                className={cn(
                    'tablet-md:hidden',
                    isCompleted ? 'space-y-[7px]' : 'flex items-center gap-1',
                )}>
                <Label className="text-sm m-0 text-muted-foreground">
                    {isCompleted ? 'Training Details:' : 'Due Date:'}
                </Label>
                <div className="text-sm">
                    {isCompleted
                        ? trainingTitle
                        : training.dueDate
                          ? formatDate(training.dueDate)
                          : 'Not specified'}
                </div>
            </div>

            <div className="laptop:hidden">
                <Label className="text-sm text-muted-foreground">
                    {isCompleted ? 'Team:' : 'Crew Members:'}
                </Label>
                <div className="flex gap-1 mt-1">
                    {members.slice(0, bp.small ? 2 : 6).map((member: any) => (
                        <>
                            {bp.small ? (
                                <Badge
                                    key={member.id}
                                    type="normal"
                                    variant="outline"
                                    className="h-8 px-2.5 text-sm">
                                    {member.firstName} {member.surname ?? ''}
                                </Badge>
                            ) : (
                                <Tooltip key={member.id}>
                                    <TooltipTrigger>
                                        <Avatar
                                            size="sm"
                                            variant={
                                                isOverdue
                                                    ? 'destructive'
                                                    : isUpcoming
                                                      ? 'warning'
                                                      : 'secondary'
                                            }>
                                            <AvatarFallback className="text-sm">
                                                {getCrewInitials(
                                                    member.firstName,
                                                    member.surname,
                                                )}
                                            </AvatarFallback>
                                        </Avatar>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                        {member.firstName}{' '}
                                        {member.surname ?? ''}
                                    </TooltipContent>
                                </Tooltip>
                            )}
                        </>
                    ))}
                    {members.length > (bp.small ? 2 : 6) && (
                        <div>
                            <Popover>
                                <PopoverTrigger className="w-fit" asChild>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        className="w-fit">
                                        +{members.length - (bp.small ? 2 : 6)}{' '}
                                        more
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent>
                                    <div className="p-3 w-64 max-h-64 overflow-auto">
                                        <div className="space-y-2">
                                            {members
                                                .slice(bp.small ? 2 : 6)
                                                .map((remainingMember: any) => (
                                                    <div
                                                        key={remainingMember.id}
                                                        className="text-sm">
                                                        {`${remainingMember.firstName ?? ''} ${remainingMember.surname ?? ''}`}
                                                    </div>
                                                ))}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        </div>
                    )}
                </div>
            </div>

            <div className="flex justify-between landscape:hidden items-center">
                <Label className="text-sm m-0 text-muted-foreground">
                    {isCompleted ? 'Location:' : 'Vessel:'}
                </Label>
                <div className="flex items-center gap-2">
                    <span className="text-sm text-nowrap">
                        {training.vessel?.title || ''}
                    </span>
                    {isCompleted && (
                        <LocationModal
                            vessel={training.vessel}
                            iconClassName="size-8"
                        />
                    )}
                </div>
            </div>
        </div>
    )
}
